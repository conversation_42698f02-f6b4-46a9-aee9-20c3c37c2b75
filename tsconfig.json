{"compilerOptions": {"baseUrl": ".", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "allowImportingTsExtensions": true, "incremental": true, "types": ["vitest/globals"], "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@/styles/*": ["./src/styles/*"], "@/assets/*": ["./src/assets/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}