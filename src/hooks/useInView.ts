import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

function useInView<T extends HTMLElement>(options?: IntersectionObserverInit) {
  const ref = useRef<T | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [inView, setInView] = useState(false);

  const callback = useCallback<IntersectionObserverCallback>(entries => {
    const [entry] = entries;
    setInView(entry.isIntersecting);
  }, []);

  // Memoize the options to avoid triggering useEffect unless values change
  const memoizedOptions = useMemo(
    () => options,
    [options?.root, options?.rootMargin, options?.threshold]
  );

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    observerRef.current?.disconnect();
    observerRef.current = new IntersectionObserver(callback, memoizedOptions);
    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [callback, memoizedOptions]);

  return { ref, inView };
}

export default useInView;
