import React, { useContext } from 'react';

interface IConfig {
  contextName: string;
  providerName: string;
}

/**
 * A safer wrapper for using React context, with helpful error messages.
 *
 * @param ReactContext - The React Context to consume.
 * @param config - Configuration object with context and provider names.
 * @returns The context value.
 */
const useContextWrapper = <T>(
  ReactContext: React.Context<T | undefined>,
  config: IConfig
): T => {
  const context = useContext(ReactContext);
  const { contextName, providerName } = config;

  if (context === undefined) {
    throw new Error(
      `${contextName} must be used within a ${providerName}.`
    );
  }

  return context;
};

export default useContextWrapper;
