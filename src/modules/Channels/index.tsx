import React from 'react';
import GenericTabbedModule from '@/modules/common/GenericTabbedModule';
import { FilterType, ChannelId, ChannelMainTab } from './enums';
import { availableChannels, filters, mainChannelTab, myChannels } from './config';
import MainContent from './MainContent'; // Keep Channels' specific MainContent for now

const ChannelsTab: React.FC = () => {
  const renderChannelContent = (channelId: string) => {
    // This will be the Channels-specific rendering logic
    return <MainContent selectedChannel={channelId as ChannelId} />;
  };

  return (
    <GenericTabbedModule
      mainTabEnum={ChannelMainTab}
      filterTypeEnum={FilterType}
      availableItems={availableChannels}
      myItems={myChannels}
      filters={filters}
      mainTabsConfig={mainChannelTab}
      emptyStateTitleKey="channels.selectChannels"
      emptyStateDescriptionKey="channels.nothingSelected"
      renderMainContent={renderChannelContent}
    />
  );
};

export default ChannelsTab;
