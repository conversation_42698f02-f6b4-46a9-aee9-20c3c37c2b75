import { GenericItem, GenericFilter, GenericTabState } from '@/modules/common/types';
import { FilterType, ChannelId, ChannelMainTab } from './enums';

export interface Channel extends GenericItem {
  id: ChannelId;
  type: 'native' | 'text' | 'voice';
  phoneNumber?: string;
  webhookUrl?: string;
}

export interface ChannelFilter extends GenericFilter {
  type: FilterType;
}

export interface ChannelTabState extends GenericTabState {
  mainTab: ChannelMainTab;
  selectedItem: ChannelId | null;
}
