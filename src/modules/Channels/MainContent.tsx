import React from 'react';
import EmptyState from '@/components/EmptyState';
import { ChannelId } from './enums';
import WhatsappView from './Whatsapp';
import WebChannelView from './WebChannel';
import { useTranslation } from 'react-i18next';

interface MainContentProps {
  selectedChannel: ChannelId | null;
}

const MainContent: React.FC<MainContentProps> = ({ selectedChannel }) => {
  const { t } = useTranslation();
  if (!selectedChannel) {
    return (
      <EmptyState
        title={t('channels.selectChannels')}
        description={t('channels.nothingSelected')}
      />
    );
  }

  switch (selectedChannel) {
    case ChannelId.WhatsApp:
      return <WhatsappView />;

    case ChannelId.WebMobileBot:
      return <WebChannelView />;

    default:
      return <EmptyState />;
  }
};

export default MainContent;
