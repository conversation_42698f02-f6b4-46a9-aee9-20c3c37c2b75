import React from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { useTranslation } from 'react-i18next';
import { WebChannelConfig } from '../types';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import ColorCard from './ColorCard';
import DefaultBotImage from '@/assets/icons/bot.svg';
import { FileUpload } from '@/components/file-upload';
import { FONT_FAMILY_OPTIONS, FONT_SIZE_OPTIONS } from '../config';

interface WidgetSettingsContentProps {
  config: WebChannelConfig;
  onConfigChange: (field: keyof WebChannelConfig, value: string) => void;
}

const WidgetSettingsContent: React.FC<WidgetSettingsContentProps> = ({
  config,
  onConfigChange,
}) => {
  const { t } = useTranslation();

  const handleImageUpload = async (file: File) => {
    //TODO: implement actual file upload
    const fileUrl = URL.createObjectURL(file);
    onConfigChange('botAvatarUrl', fileUrl);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('channels.botAvatar')}</CardTitle>
          <p className="text-sm text-muted-foreground">{t('channels.uploadAvatarDescription')}</p>
        </CardHeader>
        <CardContent className="flex gap-6 justify-between">
          <div className="flex-1 space-y-7">
            <FloatingField
              label={t('channels.botDisplayName') + '*'}
              value={config.botName}
              onChange={value => onConfigChange('botName', value)}
            />

            <FloatingField
              label={t('channels.botDescription') + '*'}
              value={config.botDescription}
              onChange={value => onConfigChange('botDescription', value)}
            />
          </div>

          <div className="w-24 h-24 rounded-full overflow-hidden border border-dashed flex items-center justify-center relative">
            <FileUpload
              onUpload={handleImageUpload}
              maxSize={5}
              value={[config.botAvatarUrl]}
              accept="image/*"
              uploadIcon={
                <div className="w-full h-full absolute top-0.5">
                  <img
                    className="w-full h-full -top-1 relative"
                    src={config.botAvatarUrl || DefaultBotImage}
                    alt="Bot Avatar"
                  />
                </div>
              }
            />
            {/* TODO: need to verify if it can be rm or useful after upload functionality is added  */}
            {/* <img
              src={config.botImage || DefaultBotImage}
              alt="Bot Avatar"
              className="w-full h-full object-cover absolute"
            /> */}
          </div>
        </CardContent>
      </Card>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-base">{t('channels.fontsAndColorTheme')}</CardTitle>
          <p className="text-sm text-muted-foreground">{t('channels.setColorThemeDescription')}</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ColorCard
              color={config.primaryColor}
              onColorChange={color => onConfigChange('primaryColor', color)}
              label={'Add a Primary color'}
            />

            <ColorCard
              color={config.secondaryColor}
              onColorChange={color => onConfigChange('secondaryColor', color)}
              label={t('channels.addSecondaryColor')}
            />

            <ColorCard
              color={config.tertiaryColor}
              onColorChange={color => onConfigChange('tertiaryColor', color)}
              label={'Add a Tertiary color'}
            />
          </div>

          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">Select the font for your widget</p>

            <div className="flex items-center gap-3">
              <FloatingField
                label={t('channels.fontFamily')}
                as={FloatingType.SELECT}
                value={config.fontFamily}
                onChange={value => onConfigChange('fontFamily', value)}
                options={FONT_FAMILY_OPTIONS}
              />

              <FloatingField
                label={t('channels.fontSize')}
                as={FloatingType.SELECT}
                value={config.fontSize}
                onChange={value => onConfigChange('fontSize', value)}
                options={FONT_SIZE_OPTIONS}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default WidgetSettingsContent;
