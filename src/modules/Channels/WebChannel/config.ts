import { TabConfig } from '@/types';

export const webChannelConfigurationTab: TabConfig[] = [
  {
    id: 'widget-settings',
    labelKey: 'channels.widgetSettings',
  },
  {
    id: 'deploy',
    labelKey: 'channels.deploy',
  },
];

export const FONT_FAMILY_OPTIONS = [
  { value: 'sans-serif', label: 'Sans-serif' },
  { value: 'serif', label: 'Serif' },
  { value: 'monospace', label: 'Monospace' },
  // Add more options as needed
];

export const FONT_SIZE_OPTIONS = [
  { value: '0.875rem', label: 'Small (14px)' },
  { value: '1rem', label: 'Medium (16px)' },
  { value: '1.125rem', label: 'Large (18px)' },
  // Add more options as needed
];
