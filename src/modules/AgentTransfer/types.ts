import { <PERSON>ricI<PERSON>, GenericFilter, GenericTabState } from '@/modules/common/types';
import { AgentTransferFilterType, AgentTransferId, AgentTransferMainTab, fieldType } from './enums';
import { FilterType } from '../Channels/enums';

export interface AgentTransfer extends GenericItem {
  id: AgentTransferId;
  type: FilterType;
  phoneNumber?: string;
  webhookUrl?: string;
}

export interface AgentTransferFilter extends GenericFilter {
  type: AgentTransferFilterType; // Override type to be specific
}

export interface AgentTransferTabState extends GenericTabState {
  mainTab: AgentTransferMainTab;
  selectedItem: AgentTransferId | null;
}

export interface AgentTransferField {
  fieldName: string;
  fieldType: fieldType;
  fieldLabel: string;
}

export interface AgentTransferPlatformConfig {
  id: AgentTransferId;
  fields: AgentTransferField[];
}
