import React from 'react';
import GenericTabbedModule from '@/modules/common/GenericTabbedModule';
import GenericMainContent from '@/modules/common/GenericMainContent';
import { AgentTransferFilterType, AgentTransferMainTab } from './enums';
import { availableAgentTransfers, filters, mainAgentTransferTab, myAgentTransfers } from './config';
import MainContent from './MainContent';

import { Bot } from '@/types'; // Import Bot type

interface AgentTransfersTabProps {
  bot: Bot;
}

const AgentTransfersTab: React.FC<AgentTransfersTabProps> = ({ bot }) => {
  const renderAgentTransferContent = (agentTransferId: string) => {
    const selectedAgentTransfer = availableAgentTransfers.find(
      transfer => transfer.id === agentTransferId
    );

    if (!selectedAgentTransfer) {
      return null; // Or handle the case where the agent transfer is not found
    }

    return (
      <GenericMainContent
        selectedItem={agentTransferId}
        renderContent={() => (
          <MainContent selectedAgentTransfer={selectedAgentTransfer} bot={bot} />
        )}
        emptyStateTitleKey="agentTransfer.selectAgentTransfer"
        emptyStateDescriptionKey="agentTransfer.nothingSelected"
      />
    );
  };

  return (
    <GenericTabbedModule
      mainTabEnum={AgentTransferMainTab}
      filterTypeEnum={AgentTransferFilterType}
      availableItems={availableAgentTransfers}
      myItems={myAgentTransfers}
      filters={filters}
      mainTabsConfig={mainAgentTransferTab}
      emptyStateTitleKey="agentTransfer.selectAgentTransfer"
      emptyStateDescriptionKey="agentTransfer.nothingSelected"
      renderMainContent={renderAgentTransferContent}
      hideTabNavigation={true}
    />
  );
};

export default AgentTransfersTab;
