export enum AgentTransferFilterType {
  ALL = 'all',
  NATIVE = 'native',
  THIRD_PARTY = 'third-party',
}

export enum AgentTransferMainTab {
  AVAILABLE = 'available',
  MY_AGENT_TRANSFERS = 'my-agent-transfers',
}

export enum AgentTransferId {
  LiveAgentPortal = 'live-agent-portal',
  Webhook = 'webhook',
  Zendesk = 'zendesk',
  Freshdesk = 'freshdesk',
  LiveChat = 'livechat',
}

export enum fieldType {
  TEXT = 'text',
  PASSWORD = 'password',
  NUMBER = 'number',
}
