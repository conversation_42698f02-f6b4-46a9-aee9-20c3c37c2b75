import { AgentTransferFilterType, AgentTransferId, AgentTransferMainTab } from './enums';
import { Agent<PERSON>ransfer, AgentTransferFilter } from './types';
import { TabConfig } from '@/types';
import { FilterType } from '@/modules/Channels/enums';

// Placeholder icons - replace with actual icons as needed
import LiveAgentPortalIcon from '@/assets/icons/LiveAgentPortal.png';
import WebhookNative from '@/assets/icons/WebhookNative.png';

export const availableAgentTransfers: AgentTransfer[] = [
  {
    id: AgentTransferId.LiveAgentPortal,
    name: 'Live Agent Portal',
    type: FilterType.NATIVE,
    status: 'available',
    icon: LiveAgentPortalIcon,
  },
  {
    id: AgentTransferId.Webhook,
    name: 'Webhook',
    type: FilterType.NATIVE,
    status: 'available',
    icon: WebhookNative,
  },
  {
    id: AgentTransferId.Zendesk,
    name: 'Zendesk',
    type: FilterType.TEXT, // Assuming 'text' for third-party integrations
    status: 'available',
    icon: LiveAgentPortalIcon,
  },
  {
    id: AgentTransferId.Freshdesk,
    name: 'Freshdesk',
    type: FilterType.TEXT, // Assuming 'text' for third-party integrations
    status: 'available',
    icon: LiveAgentPortalIcon,
  },
  {
    id: AgentTransferId.LiveChat,
    name: 'LiveChat',
    type: FilterType.TEXT, // Assuming 'text' for third-party integrations
    status: 'available',
    icon: LiveAgentPortalIcon,
  },
];

export const myAgentTransfers: AgentTransfer[] = [
  // Example of a connected agent transfer, can be empty initially
  // {
  //   id: AgentTransferId.LiveAgentPortal,
  //   name: 'Live Agent Portal',
  //   type: 'native',
  //   status: 'connected',
  //   icon: LiveAgentPortalIcon,
  //   webhookUrl: 'https://ngage.cpaas.com/v1/bot/agent_transfer_portal',
  //   isVerified: true,
  // },
];

//dummy data for agent transfer

export const filters: AgentTransferFilter[] = [
  { id: 'all', labelKey: 'agentTransfer.filters.all', type: AgentTransferFilterType.ALL },
  { id: 'native', labelKey: 'agentTransfer.filters.native', type: AgentTransferFilterType.NATIVE },
  {
    id: 'third-party',
    labelKey: 'agentTransfer.filters.thirdParty',
    type: AgentTransferFilterType.THIRD_PARTY,
  },
];

export const mainAgentTransferTab: TabConfig[] = [
  {
    id: AgentTransferMainTab.AVAILABLE,
    labelKey: 'agentTransfer.tabs.available',
  },
  {
    id: AgentTransferMainTab.MY_AGENT_TRANSFERS,
    labelKey: 'agentTransfer.tabs.myAgentTransfers',
  },
];

export const agentTransferPlatformConfigs = [
  {
    id: AgentTransferId.LiveAgentPortal,
    fields: [
      { fieldName: 'clientId', fieldType: 'text', fieldLabel: 'Client ID', required: true },
      { fieldName: 'channelId', fieldType: 'text', fieldLabel: 'Channel ID', required: true },
    ],
  },
];
