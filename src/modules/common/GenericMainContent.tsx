import React from 'react';
import EmptyState from '@/components/EmptyState';
import { useTranslation } from 'react-i18next';
import { GenericMainContentProps } from './types';

const GenericMainContent: React.FC<GenericMainContentProps> = ({
  selectedItem,
  renderContent,
  emptyStateTitleKey,
  emptyStateDescriptionKey,
}) => {
  const { t } = useTranslation();
  if (!selectedItem) {
    return <EmptyState title={t(emptyStateTitleKey)} description={t(emptyStateDescriptionKey)} />;
  }

  return renderContent(selectedItem);
};

export default GenericMainContent;
