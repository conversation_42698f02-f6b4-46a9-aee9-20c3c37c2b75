import React from 'react';

export interface GenericItem {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'connected' | 'available';
  icon: string;
  description?: string;
  isVerified?: boolean;
  isConnected?: boolean;
  phoneNumber?: string;
  webhookUrl?: string;
  color?: string;
}

export interface GenericFilter {
  id: string;
  labelKey: string;
  type: string;
}

export interface GenericTabState {
  mainTab: string;
  searchQuery: string;
  activeFilter: string;
  selectedItem: string | null;
}

export interface GenericMainContentProps {
  selectedItem: string | null;
  renderContent: (itemId: string) => React.ReactNode;
  emptyStateTitleKey: string;
  emptyStateDescriptionKey: string;
}

export interface GenericSidebarContainerProps {
  activeTab: string;
  items: GenericItem[];
  selectedItem: string | null;
  searchQuery: string;
  activeFilter: string;
  filters: GenericFilter[];
  onItemSelect: (itemId: string) => void;
  onSearchChange: (query: string) => void;
  onFilterChange: (filterId: string) => void;
  sidebarComponent: React.ComponentType<GenericSidebarProps>;
  filterTypeEnum: any;
}

export interface GenericSidebarProps {
  items: GenericItem[];
  selectedItem: string | null;
  searchQuery: string;
  activeFilter: string;
  filters: GenericFilter[];
  onItemSelect: (itemId: string) => void;
  onSearchChange: (query: string) => void;
  onFilterChange: (filterId: string) => void;
  searchComponent: React.ComponentType<GenericSearchProps>;
  filterComponent: React.ComponentType<GenericFilterComponentProps>;
  listItemComponent: React.ComponentType<GenericListItemProps>;
  filterTypeEnum: any; // This will be the specific enum (FilterType or AgentTransferFilterType)
}

export interface GenericSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export interface GenericFilterComponentProps {
  filters: GenericFilter[];
  activeFilter: string;
  onFilterChange: (filterId: string) => void;
}

export interface GenericListItemProps {
  item: GenericItem;
  isSelected: boolean;
  onSelect: (itemId: string) => void;
}

export interface GenericTabNavigationProps {
  activeTab: string;
  tabs: { id: string; labelKey: string }[];
  onTabChange: (value: string) => void;
}
