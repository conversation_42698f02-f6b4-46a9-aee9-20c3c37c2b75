import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { GenericTabNavigationProps } from './types';

const GenericTabNavigation: React.FC<GenericTabNavigationProps> = ({
  activeTab,
  tabs,
  onTabChange,
}) => {
  const { t } = useTranslation();

  return (
    <TabsList className="w-full gap-3 justify-start p-3 pb-0 border-b border-tertiary-300">
      {tabs.map(tab => (
        <TabsTrigger
          key={tab.id}
          value={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={cn('!shadow-none rounded-none !py-2 border-b-2 border-transparent', {
            'border-primary': tab.id === activeTab,
            'text-muted-foreground': tab.id !== activeTab,
          })}
        >
          {t(tab.labelKey)}
        </TabsTrigger>
      ))}
    </TabsList>
  );
};

export default GenericTabNavigation;
