import React from 'react';
import { TabsContent } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { GenericSidebarContainerProps } from './types';
import GenericSidebarSearch from './GenericSidebarSearch';
import GenericSidebarFilter from './GenericSidebarFilter';
import GenericSidebarListItem from './GenericSidebarListItem';

const GenericSidebarContainer: React.FC<GenericSidebarContainerProps> = ({
  activeTab,
  items,
  selectedItem,
  searchQuery,
  activeFilter,
  filters,
  onItemSelect,
  onSearchChange,
  onFilterChange,
  sidebarComponent: SidebarComponent,
  filterTypeEnum,
}) => {
  return (
    <>
      <TabsContent
        key={activeTab}
        value={activeTab}
        className={cn('h-0 flex flex-col mt-0', {
          'flex-1': true,
        })}
      >
        <SidebarComponent
          items={items}
          selectedItem={selectedItem}
          searchQuery={searchQuery}
          activeFilter={activeFilter}
          filters={filters}
          onItemSelect={onItemSelect}
          onSearchChange={onSearchChange}
          onFilterChange={onFilterChange}
          searchComponent={GenericSidebarSearch}
          filterComponent={GenericSidebarFilter}
          listItemComponent={GenericSidebarListItem}
          filterTypeEnum={filterTypeEnum}
        />
      </TabsContent>
    </>
  );
};

export default GenericSidebarContainer;
