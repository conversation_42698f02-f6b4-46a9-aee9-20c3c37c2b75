import React, { useState } from 'react';
import { Tabs } from '@/components/ui/tabs';
import { GenericTabState, GenericItem, GenericFilter } from './types';
import GenericTabNavigation from './GenericTabNavigation';
import GenericSidebar from './GenericSidebar';
import GenericSidebarContainer from './GenericSidebarContainer';
import GenericMainContent from './GenericMainContent';
import { FilterType, ChannelMainTab } from '../Channels/enums';
import { AgentTransferFilterType, AgentTransferMainTab } from '../AgentTransfer/enums';

interface GenericTabbedModuleProps {
  mainTabEnum: typeof ChannelMainTab | typeof AgentTransferMainTab;
  filterTypeEnum: typeof FilterType | typeof AgentTransferFilterType;
  availableItems: GenericItem[];
  myItems: GenericItem[];
  filters: GenericFilter[];
  mainTabsConfig: { id: string; labelKey: string }[];
  hideTabNavigation?: boolean;
  emptyStateTitleKey: string;
  emptyStateDescriptionKey: string;
  renderMainContent: (itemId: string) => React.ReactNode;
}

const GenericTabbedModule: React.FC<GenericTabbedModuleProps> = ({
  mainTabEnum,
  filterTypeEnum,
  availableItems,
  myItems,
  filters,
  mainTabsConfig,
  emptyStateTitleKey,
  emptyStateDescriptionKey,
  renderMainContent,
  hideTabNavigation,
}) => {
  const [state, setState] = useState<GenericTabState>({
    mainTab: mainTabEnum.AVAILABLE,
    searchQuery: '',
    activeFilter: filterTypeEnum.ALL,
    selectedItem: null,
  });

  const handleItemSelect = (itemId: string) => {
    setState(prev => ({ ...prev, selectedItem: itemId }));
  };

  const handleSearchChange = (query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
  };

  const handleFilterChange = (filterId: string) => {
    setState(prev => ({ ...prev, activeFilter: filterId }));
  };

  const currentItems = state.mainTab === mainTabEnum.AVAILABLE ? availableItems : myItems;

  return (
    <Tabs
      value={state.mainTab}
      onValueChange={value => setState(prev => ({ ...prev, mainTab: value }))}
      className="flex flex-1 h-0 w-full"
    >
      <div className="flex flex-col h-full w-[440px] border-r border-tertiary-300">
        {!hideTabNavigation && (
          <GenericTabNavigation
            activeTab={state.mainTab}
            tabs={mainTabsConfig}
            onTabChange={value => setState(prev => ({ ...prev, mainTab: value }))}
          />
        )}
        <GenericSidebarContainer
          activeTab={state.mainTab}
          items={currentItems}
          selectedItem={state.selectedItem}
          searchQuery={state.searchQuery}
          activeFilter={state.activeFilter}
          filters={filters}
          onItemSelect={handleItemSelect}
          onSearchChange={handleSearchChange}
          onFilterChange={handleFilterChange}
          sidebarComponent={GenericSidebar}
          filterTypeEnum={filterTypeEnum}
        />
      </div>
      <GenericMainContent
        selectedItem={state.selectedItem}
        renderContent={renderMainContent}
        emptyStateTitleKey={emptyStateTitleKey}
        emptyStateDescriptionKey={emptyStateDescriptionKey}
      />
    </Tabs>
  );
};

export default GenericTabbedModule;
