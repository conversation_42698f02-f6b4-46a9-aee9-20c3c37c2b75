import React from 'react';
import { GenericSidebarProps } from './types';

const GenericSidebar: React.FC<GenericSidebarProps> = ({
  items,
  selectedItem,
  searchQuery,
  activeFilter,
  filters,
  onItemSelect,
  onSearchChange,
  onFilterChange,
  searchComponent: SearchComponent,
  filterComponent: FilterComponent,
  listItemComponent: ListItemComponent,
  filterTypeEnum,
}) => {
  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = activeFilter === filterTypeEnum.ALL || item.type === activeFilter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="border-tertiary-200 flex flex-col h-0 flex-1">
      {/* Search */}
      <SearchComponent searchQuery={searchQuery} onSearchChange={onSearchChange} />

      {/* Filters */}
      <FilterComponent
        activeFilter={activeFilter}
        filters={filters}
        onFilterChange={onFilterChange}
      />

      {/* Item List */}
      <div className="flex-1 overflow-y-auto">
        {filteredItems.map(item => (
          <ListItemComponent
            key={item.id}
            item={item}
            isSelected={selectedItem === item.id}
            onSelect={onItemSelect}
          />
        ))}
      </div>
    </div>
  );
};

export default GenericSidebar;
