import { Command, EditorState, NodeSelection, TextSelection, Transaction } from 'prosemirror-state';
import { toggleMark, setBlockType, wrapIn, baseKeymap } from 'prosemirror-commands';
import { wrapInList, liftListItem, sinkListItem, splitListItem } from 'prosemirror-schema-list';
import { schema } from './schema';
import { MarkType, NodeType } from 'prosemirror-model';
import type { CommandDispatch } from '../types';
import type { EditorView } from 'prosemirror-view';
export const insertEmoji =
  (char: string): Command =>
  (state, dispatch) => {
    const emojiNode = schema.nodes.emoji.create({ char });
    if (dispatch) {
      dispatch(state.tr.replaceSelectionWith(emojiNode).scrollIntoView());
    }
    return true;
  };

export const commands = {
  toggleBold: toggleMark(schema.marks.strong),
  toggleItalic: toggleMark(schema.marks.em),
  toggleUnderline: toggleMark(schema.marks.underline),
  toggleStrike: toggleMark(schema.marks.strike),
  toggleHighlight: toggleMark(schema.marks.highlight),
  insertEmoji,
  toggleSupSub: (state: EditorState, dispatch?: CommandDispatch) => {
    const { from, to } = state.selection;
    const tr = state.tr;
    const hasSuperscript = state.doc.rangeHasMark(from, to, schema.marks.superscript);
    const hasSubscript = state.doc.rangeHasMark(from, to, schema.marks.subscript);

    if (hasSuperscript) {
      tr.removeMark(from, to, schema.marks.superscript);
    } else if (hasSubscript) {
      tr.removeMark(from, to, schema.marks.subscript);
      tr.addMark(from, to, schema.marks.superscript.create());
    } else {
      tr.addMark(from, to, schema.marks.subscript.create());
    }

    if (dispatch) dispatch(tr);
    return true;
  },
  lineBreakEnter: (state: EditorState, dispatch?: CommandDispatch) =>
    splitListItem(schema.nodes.list_item)(state, dispatch) || baseKeymap.Enter(state, dispatch),
  makeParagraph: setBlockType(schema.nodes.paragraph),
  makeHeading1: setBlockType(schema.nodes.heading, { level: 1 }),
  makeHeading2: setBlockType(schema.nodes.heading, { level: 2 }),
  makeHeading3: setBlockType(schema.nodes.heading, { level: 3 }),
  makeCodeBlock: setBlockType(schema.nodes.code_block),
  wrapBulletList: (state: EditorState, dispatch?: CommandDispatch) => {
    const { $from } = state.selection;
    const actions = {
      [schema.nodes.ordered_list.name]: () => {
        const tr = state.tr;
        const pos = $from.before($from.depth);
        if (dispatch) dispatch(tr.setNodeMarkup(pos, schema.nodes.bullet_list));
        return true;
      },
      [schema.nodes.bullet_list.name]: () => liftListItem(schema.nodes.list_item)(state, dispatch),
    };

    for (let d = $from.depth; d >= 0; d--) {
      const node = $from.node(d);
      const action = actions[node.type.name];
      if (action) return action();
    }
    return wrapInList(schema.nodes.bullet_list)(state, dispatch) || false;
  },
  wrapOrderedList: (state: EditorState, dispatch?: CommandDispatch) => {
    const { $from } = state.selection;
    const actions = {
      [schema.nodes.bullet_list.name]: () => {
        const tr = state.tr;
        const pos = $from.before($from.depth);
        if (dispatch) dispatch(tr.setNodeMarkup(pos, schema.nodes.ordered_list));
        return true;
      },
      [schema.nodes.ordered_list.name]: () => liftListItem(schema.nodes.list_item)(state, dispatch),
    };

    for (let d = $from.depth; d >= 0; d--) {
      const node = $from.node(d);
      const action = actions[node.type.name];
      if (action) return action();
    }
    return wrapInList(schema.nodes.ordered_list)(state, dispatch) || false;
  },
  liftListItem: liftListItem(schema.nodes.list_item),
  sinkListItem: sinkListItem(schema.nodes.list_item),
  wrapBlockquote: wrapIn(schema.nodes.blockquote),
};

export function isMarkActive(state: EditorState, markType: MarkType): boolean {
  if (!markType) return false;
  const { from, $from, to, empty } = state.selection;
  if (empty) {
    const marks = state.storedMarks ?? $from.marks();
    return marks ? !!markType.isInSet(marks) : false;
  }
  return state.doc.rangeHasMark(from, to, markType);
}

export const isBlockActive = (state: EditorState, nodeType: NodeType, attrs = {}): boolean => {
  const { $from, to } = state.selection;
  const node = state.selection instanceof NodeSelection ? state.selection.node : null;
  return node
    ? node.hasMarkup(nodeType, attrs)
    : to <= $from.end() && !!$from.parent?.hasMarkup(nodeType, attrs);
};

export const isListActive = (state: EditorState, listType: NodeType): boolean =>
  [state.selection.$from].some($from =>
    Array.from({ length: $from.depth + 1 }, (_, d) => $from.node(d)).some(
      node => node.type === listType
    )
  );

export const toggleSuperscript = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  const { from, to } = state.selection;
  const tr = state.tr;
  const hasSuperscript = state.doc.rangeHasMark(from, to, schema.marks.superscript);
  const hasSubscript = state.doc.rangeHasMark(from, to, schema.marks.subscript);

  if (hasSuperscript) {
    tr.removeMark(from, to, schema.marks.superscript);
  } else {
    if (hasSubscript) {
      tr.removeMark(from, to, schema.marks.subscript);
    }
    tr.addMark(from, to, schema.marks.superscript.create());
  }

  if (dispatch) dispatch(tr);
  return true;
};

export const toggleSubscript = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  const { from, to } = state.selection;
  const tr = state.tr;
  const hasSubscript = state.doc.rangeHasMark(from, to, schema.marks.subscript);
  const hasSuperscript = state.doc.rangeHasMark(from, to, schema.marks.superscript);

  if (hasSubscript) {
    tr.removeMark(from, to, schema.marks.subscript);
  } else {
    if (hasSuperscript) {
      tr.removeMark(from, to, schema.marks.superscript);
    }
    tr.addMark(from, to, schema.marks.subscript.create());
  }

  if (dispatch) dispatch(tr);
  return true;
};

export const insertLink = (view: EditorView) => (href: string, title?: string) => {
  const { state, dispatch } = view;
  const { from, to, empty } = state.selection;

  const linkMark = schema.marks.link.create({ href });
  const linkText = title ?? href;

  if (empty) {
    // No selection, insert link text at cursor
    dispatch(
      state.tr
        .insertText(linkText, from)
        .addMark(from, from + linkText.length, linkMark)
        .scrollIntoView()
    );
  } else {
    // Text selected, apply link to selected range
    dispatch(state.tr.addMark(from, to, linkMark).scrollIntoView());
  }

  view.focus();
};
export const updateLink = (view: EditorView) => (href: string, displayText?: string) => {
  const { state, dispatch } = view;
  const { from, to } = state.selection;
  const tr = state.tr;

  const newDisplayText = displayText ?? href;
  const linkMark = schema.marks.link.create({ href, title: newDisplayText });

  // Always remove the old link mark from the selection range.
  tr.removeMark(from, to, schema.marks.link);

  // If the text content is changing, replace it.
  if (newDisplayText !== state.doc.textBetween(from, to)) {
    // Replace the content and apply the new mark in the same step.
    tr.replaceWith(from, to, schema.text(newDisplayText, [linkMark]));
  } else {
    // If only the href is changing, just add the new mark.
    tr.addMark(from, to, linkMark);
  }

  dispatch(tr.scrollIntoView());
};
export const removeLink = (view: EditorView) => (href?: string) => {
  const { state, dispatch } = view;
  let { from, to } = state.selection;

  // If href is provided, find the link with that href
  if (href) {
    let found = false;
    state.doc.descendants((node, pos) => {
      if (found) return false;
      if (node.isText) {
        const marks = node.marks.filter(
          mark => mark.type.name === 'link' && mark.attrs.href === href
        );
        if (marks.length > 0) {
          from = pos;
          to = pos + node.nodeSize;
          found = true;
          console.log('Found link at:', { from, to });
        }
      }
    });
    if (!found) {
      console.log('No link found with href:', href);
      return false;
    }
  }

  // Remove the link mark from the range
  const tr = state.tr.removeMark(from, to, schema.marks.link);

  // Set the selection to a collapsed position at the start of the range
  tr.setSelection(TextSelection.create(tr.doc, from));

  dispatch(tr);
  console.log('Link removed successfully, selection set to:', from);
  view.focus(); // Ensure the editor retains focus
  return true;
};
