import { Schema } from 'prosemirror-model';
import { schema as basicSchema } from 'prosemirror-schema-basic';
import { addListNodes } from 'prosemirror-schema-list';
import type { NodeMark } from '../types';

const marks = basicSchema.spec.marks.append({
  underline: {
    parseDOM: [{ tag: 'u' }],
    toDOM: () => ['u', { class: 'underline' }, 0],
  },
  strike: {
    parseDOM: [{ tag: 's' }],
    toDOM: () => ['s', { class: 'line-through' }, 0],
  },
  highlight: {
    parseDOM: [{ tag: 'mark' }],
    toDOM() {
      return ['mark', { className: 'bg-warning-200' }, 0];
    },
  },
  superscript: {
    parseDOM: [{ tag: 'sup' }],
    toDOM: () => ['sup', 0],
  },
  subscript: {
    parseDOM: [{ tag: 'sub' }],
    toDOM: () => ['sub', 0],
  },
  link: {
    attrs: { href: {}, title: { default: null } },
    inclusive: false,
    parseDOM: [
      {
        tag: 'a[href]',
        getAttrs(dom) {
          return { href: dom.getAttribute('href'), title: dom.getAttribute('title') };
        },
      },
    ],
    toDOM(node: NodeMark) {
      return ['a', { href: node.attrs.href, title: node.attrs.title, target: '_blank' }, 0];
    },
  },
});
const emojiNodeSpec = {
  group: 'inline',
  inline: true,
  atom: true,
  selectable: false,
  draggable: false,
  attrs: { char: {} },
  parseDOM: [
    {
      tag: 'img[emoji]',
      getAttrs: (dom: HTMLElement) => ({
        char: dom.getAttribute('alt'),
      }),
    },
  ],
  toDOM: (node: any): readonly [string, any, string] => [
    'span',
    {
      'data-emoji': node.attrs.char,
      style: 'font-size:1em;vertical-align:middle;',
      class: 'emoji',
    },
    node.attrs.char || '😀',
  ],
};

export const schema = new Schema({
  nodes: addListNodes(
    basicSchema.spec.nodes.append({
      emoji: emojiNodeSpec,
    }),
    'paragraph block*',
    'block'
  ),
  marks,
});
