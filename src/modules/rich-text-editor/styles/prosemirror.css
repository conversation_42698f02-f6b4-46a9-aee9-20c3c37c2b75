/* ProseMirror Editor Styles */
.ProseMirror {
  @apply outline-none relative leading-[1.5];
}

.ProseMirror[data-placeholder]:empty::before {
  content: attr(data-placeholder);
  @apply text-gray-400 absolute pointer-events-none;
}

.ProseMirror a {
  @apply text-primary underline;
}

.ProseMirror sup {
  @apply align-super text-[0.8em];
}

.ProseMirror sub {
  @apply align-sub text-[0.8em];
}

button sup,
button sub {
  margin-left: 2px;
}

.ProseMirror h1 {
  @apply text-3xl font-bold my-2;
}

.ProseMirror h2 {
  @apply text-2xl font-bold my-2;
}

.ProseMirror h3 {
  @apply text-xl font-bold my-2;
}

.ProseMirror p {
  @apply my-2;
}

.ProseMirror ul {
  @apply pl-6 my-2 list-disc list-outside;
}

.ProseMirror ol {
  @apply pl-6 my-2 list-decimal list-outside;
}

.ProseMirror li {
  @apply my-1;
}

.ProseMirror blockquote {
  @apply border-l-4 border-gray-300 pl-4 my-4 italic text-gray-600;
}

.ProseMirror pre {
  @apply bg-gray-100 rounded-md p-4 my-4 overflow-x-auto;
}

.ProseMirror code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.ProseMirror pre code {
  @apply bg-transparent p-0;
}

.ProseMirror u {
  @apply underline;
}

.ProseMirror strong {
  @apply font-bold;
}

.ProseMirror em {
  @apply italic;
}
