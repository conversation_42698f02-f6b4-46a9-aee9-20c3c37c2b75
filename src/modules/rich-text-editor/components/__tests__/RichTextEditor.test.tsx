import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import RichTextEditor from '../RichTextEditor';

describe('RichTextEditor Acceptance Criteria Tests', () => {
  const mockOnChange = vi.fn(contain => {
    console.log('wekugjdyhewgv', contain);
  });
  const user = userEvent.setup();

  beforeEach(() => {
    Object.defineProperty(document, 'elementFromPoint', {
      value: vi.fn(() => document.createElement('div')),
      configurable: true,
    });
    Range.prototype.getBoundingClientRect = vi.fn(() => ({
      x: 0,
      y: 0,
      width: 100,
      height: 20,
      top: 0,
      right: 100,
      bottom: 20,
      left: 0,
      toJSON: () => ({}),
    }));
    Range.prototype.getClientRects = () => ({
      item: () => ({
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: 0,
        height: 0,
        x: 0,
        y: 0,
        toJSON: () => ({}),
      }),
      length: 0,
      [Symbol.iterator]: vi.fn(),
    });
  });

  // AC 1: As an Agent Admin or user, I shall be able to see the “T” icon in my chat input toolbar to access rich text formatting options.
  it('AC1: should display the rich text formatting toolbar', () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    expect(screen.getByTestId('menu-bar')).toBeInTheDocument();
  });

  // AC 2: As an Agent Admin or user, I shall be able to click the “T” icon and view a popup containing options like Bold (B), Italics (I), Underline (U), Strikethrough (S), Highlight, Superscript (x²), and Subscript (x₂).
  it('AC2: should display all formatting options in the toolbar', () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    expect(screen.getByTestId('bold')).toBeInTheDocument();
    expect(screen.getByTestId('italic')).toBeInTheDocument();
    expect(screen.getByTestId('underline')).toBeInTheDocument();
    expect(screen.getByTestId('strike-through')).toBeInTheDocument();
    expect(screen.getByTestId('highlight')).toBeInTheDocument();
    expect(screen.getByTestId('superscript-subscript')).toBeInTheDocument();
  });

  // AC 3: As an Agent Admin or user, I shall be able to select text and apply any formatting style (like bold or underline), and the formatting shall be applied immediately to the selected text.
  it('AC3: should apply formatting to the selected text', async () => {
    render(<RichTextEditor content="<p>Test Bold</p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.keyboard('{Control>}{a}{/Control}');
    fireEvent.click(screen.getByTestId('bold'));
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<strong>Test Bold</strong>'));
    });
  });

  // AC 4 & 12: As an Agent Admin or user, I shall be able to apply a formatting style without selecting any text, and the selected style will automatically apply to all upcoming typed text until toggled off.
  it('AC4 & AC12: should apply formatting to upcoming text when no text is selected', async () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    user.click(screen.getByTestId('underline'));
    await user.click(editor);
    await user.keyboard('Underlined');
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('Underlined</u>'));
    });
  });

  // AC 5: As an Agent Admin or user, I shall be able to view my styled text correctly inside the input field before sending (e.g., bold text appearing bolded).
  it('AC5: should display styled text correctly in the editor', () => {
    render(<RichTextEditor content="<p><strong>Bold Text</strong></p>" onChange={mockOnChange} />);
    const editorContent = screen.getByTestId('editor-content');
    expect(editorContent.innerHTML).toContain('<strong>Bold Text</strong>');
  });

  // AC 6: As an Agent Admin or user, I shall be able to send a message with formatting, and it shall be rendered properly for both myself and the end customer.
  it('AC6: should render formatted messages correctly in read-only mode', () => {
    render(<RichTextEditor content="<p><em>Italic Text</em></p>" readOnly />);
    const editorContent = screen.getByTestId('editor-content');
    expect(editorContent.innerHTML).toContain('<em>Italic Text</em>');
  });

  // AC 7: As an Agent Admin or user, I shall be able to apply multiple formatting styles together (e.g., bold and italics) and expect them to render accurately without interfering with one another.
  it('AC7: should apply multiple formatting styles to the same text', async () => {
    render(<RichTextEditor content="<p>Combined</p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.keyboard('{Control>}{a}{/Control}');
    await user.click(screen.getByTestId('italic'));
    await user.click(screen.getByTestId('bold'));
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<em><strong>Combined</strong></em>'));
    });
  });

  // AC 8: As an Agent Admin or user, I shall be able to toggle any formatting style off by clicking on the formatting icon again, for either selected or upcoming text.
  it('AC8: should toggle formatting style off', async () => {
    render(<RichTextEditor content="<p>Underlined</p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.keyboard('{Control>}{a}{/Control}');
    fireEvent.click(screen.getByTestId('underline')); // Apply
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('Underlined</u>'));
    });
    fireEvent.click(screen.getByTestId('underline')); // Toggle off
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('Underlined</p>'));
    });
  });

  // AC 9: As an Agent Admin or user, I shall be able to edit a partially written or drafted message, and all formatting applied previously shall be retained during edits.
  it('AC9: should retain formatting when editing a draft message', async () => {
    render(<RichTextEditor content="<p><u>Underlined Draft</u></p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    const textLength = editor.textContent?.length;
    await user.click(editor);
    for (let index = 0; index <= textLength!; index++) {
      await user.keyboard('{ArrowRight}');
    }
    await user.keyboard(' More text');
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('Underlined Draft More text</u>'));
    });
  });

  // AC 10: As an Agent Admin or user, I shall be able to copy and paste formatted text, and the formatting shall be preserved during the paste operation (if supported by the editor).
  it('AC10: should preserve formatting when pasting formatted text', async () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);

    await user.paste('<strong>Bold Pasted</strong>');
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<strong>Bold Pasted</strong>'));
    });
  });

  // AC 11: As an Agent Admin or user, I shall be able to paste plain text into the chat input without it corrupting or affecting existing formatting in the input box.
  it('AC11: should paste plain text without corrupting existing formatting', async () => {
    render(<RichTextEditor content="<p><strong>Bold</strong></p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    const textLength = editor.textContent?.length;
    await user.click(editor);
    for (let index = 0; index <= textLength!; index++) {
      await user.keyboard('{ArrowRight}');
    }
    await user.paste('Plain');
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<strong>Bold</strong>Plain'));
    });
  });

  // AC 11 (Sanitization): As an Agent Admin or user, I shall be able to see my messages in a clean, readable format, even if unsupported formatting is attempted (such formatting should be sanitized and removed).
  it('AC11: should sanitize unsupported HTML to prevent XSS', async () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.paste("<script>alert('XSS')</script>");
    await waitFor(() => {
      expect(mockOnChange).not.toHaveBeenCalledWith(expect.stringContaining('<script>'));
    });
  });

  // AC 13: As an Agent Admin or user, I shall be able to combine styles (e.g., strikethrough + highlight) and expect intuitive behavior when toggling one or more styles off.
  it('AC13: should handle combining and toggling multiple styles correctly', async () => {
    render(<RichTextEditor content="<p>Styled</p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.keyboard('{Control>}{a}{/Control}');
    fireEvent.click(screen.getByTestId('strike-through'));
    fireEvent.click(screen.getByTestId('highlight'));
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('Styled</mark></s>'));
    });
    fireEvent.click(screen.getByTestId('highlight')); // Toggle off highlight
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('Styled</s>'));
    });
  });

  // AC 14: As an Agent Admin or user, I shall be able to format messages that include emojis, special characters, and links, and the editor shall function properly without breaking layout or causing errors.
  it('AC14: should format text with emojis and special characters correctly', async () => {
    render(<RichTextEditor content="<p>😀 ©</p>" onChange={mockOnChange} />);

    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.keyboard('{Control>}{a}{/Control}');
    await user.click(screen.getByTestId('bold'));

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<strong>😀 ©</strong>'));
    });
  });

  // AC 15: As an Agent Admin or user, I shall be able to move my cursor within a formatted sentence and continue typing with the existing style or disable it selectively as required.
  it('AC15: should continue typing with the existing style when the cursor is moved', async () => {
    render(<RichTextEditor content="<p><strong>Bold Start</strong></p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    const textLength = editor.textContent?.length;
    // const prosemirror = editor.('.ProseMirror');

    await user.click(editor);
    for (let index = 0; index <= textLength!; index++) {
      await user.keyboard('{ArrowRight}');
    }
    await user.keyboard(' More Bold');
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<strong>Bold Start More Bold</strong>'));
    });
  });

  // Additional Negative Test Cases for RichTextEditor

  // AC-N1: Toolbar should NOT be visible in read-only mode
  it('AC-N1: should NOT display the toolbar in read-only mode', () => {
    render(<RichTextEditor content="" readOnly onChange={mockOnChange} />);
    expect(screen.queryByTestId('menu-bar')).not.toBeInTheDocument();
  });

  // AC-N2: Formatting icons should NOT be clickable in read-only mode
  it('AC-N2: should NOT allow formatting icons to be clicked in read-only mode', async () => {
    render(<RichTextEditor content="<p>Text</p>" readOnly onChange={mockOnChange} />);
    expect(screen.queryByTestId('bold')).not.toBeInTheDocument();
  });

  // AC-N3: Applying formatting with no editor mounted should do nothing
  it('AC-N3: should NOT throw error applying formatting when editor is not mounted', () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-content');
    editor.innerHTML = ''; // Simulate no editor instance
    expect(() => fireEvent.click(screen.getByTestId('bold'))).not.toThrow();
  });

  // AC-N4: Pasting malformed HTML sanitizes dangerous tags
  it('AC-N4: should sanitize invalid/malformed HTML content', async () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.paste('<div><script>alert(1)</script><b>Safe</b></div>');

    await waitFor(() => {
      expect(mockOnChange).not.toHaveBeenCalledWith(expect.stringContaining('<script>'));
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining('<p><strong>Safe</strong></p>'));
    });
  });

  // AC-N5: Combining conflicting styles shouldn't corrupt markup
  it('AC-N5: should prevent invalid combination of superscript and subscript', async () => {
    render(<RichTextEditor content="<p>Conflict</p>" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    await user.click(editor);
    await user.keyboard('{Control>}{a}{/Control}');
    await user.click(screen.getByTestId('superscript-subscript'));
    await user.click(screen.getByTestId('superscript-subscript'));

    await waitFor(() => {
      const calls = mockOnChange.mock.calls.flat().join('');
      expect(calls).not.toContain('<sup><sub>');
      expect(calls).not.toContain('<sub><sup>');
    });
  });

  // AC-N6: Large content paste should NOT crash the editor
  it('AC-N6: should handle large pasted content without crashing', async () => {
    render(<RichTextEditor content="" onChange={mockOnChange} />);
    const editor = screen.getByTestId('editor-view');
    const largeText = 'x'.repeat(10000);

    await user.click(editor);
    await user.paste(largeText);

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(expect.stringContaining(largeText));
    });
  });

  // AC-N7: onChange should handle null or undefined safely
  it('AC-N7: should not throw error if onChange receives null or undefined', () => {
    const safeOnChange = vi.fn();
    render(<RichTextEditor content={undefined} onChange={safeOnChange} readOnly />);
    expect(() => {
      safeOnChange(null);
      safeOnChange(undefined);
    }).not.toThrow();
  });
});
