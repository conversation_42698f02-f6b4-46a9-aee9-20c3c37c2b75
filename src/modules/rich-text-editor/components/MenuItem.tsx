import {cn} from '@/lib/utils';
import type {Command} from 'prosemirror-state';
import React from 'react';

interface MenuItemProps {
  title: string;
  command: Command;
  isActive?: boolean;
  children: React.ReactNode;
  onExecute: (command: Command) => void;
  'data-testid'?: string;
}

const MenuItem: React.FC<MenuItemProps> = ({
  title,
  command,
  isActive = false,
  children,
  onExecute,
  'data-testid': testId,
}) => (
  <button
    type="button"
    title={title}
    className={cn(
      `px-2 py-1 rounded text-sm font-medium transition-colors text-tertiary-700 ${
        isActive ? 'bg-primary-100' : 'hover:bg-tertiary-200'
      } disabled:opacity-50 disabled:cursor-not-allowed`,
    )}
    onClick={() => onExecute(command)}
    data-testid={testId}
  >
    {children}
  </button>
);

export default MenuItem;
