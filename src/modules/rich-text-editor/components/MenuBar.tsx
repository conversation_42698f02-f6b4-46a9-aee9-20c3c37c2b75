import React, { useRef, useState } from 'react';
import type { Command, EditorState } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import { commands, isMarkActive } from '../utils/commands';
import { schema } from '../utils/schema';
import MenuItem from './MenuItem';
import { Smile } from 'lucide-react';
import { RTEditorState } from '../types';
import EmojiPicker, { EmojiClickData } from 'emoji-picker-react';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';

interface MenuBarProps {
  editorState: EditorState;
  editorView: EditorView;
}

interface MenuItemConfig {
  title: string;
  command: Command;
  content: React.ReactNode | ((state: EditorState) => React.ReactNode);
  isActive?: (state: EditorState) => boolean;
  testId: string;
}

// Define menuItems outside the component
const menuItems = (
  setShowEmojiPicker: React.Dispatch<React.SetStateAction<boolean>>,
  t: TFunction
): (MenuItemConfig | 'separator')[] => [
  {
    title: t('richTextEditor.bold'),
    command: commands.toggleBold,
    content: <strong className="font-bold">B</strong>,
    isActive: state => isMarkActive(state, schema.marks.strong),
    testId: 'bold',
  },
  {
    title: t('richTextEditor.italic'),
    command: commands.toggleItalic,
    content: <em className="italic">I</em>,
    isActive: state => isMarkActive(state, schema.marks.em),
    testId: 'italic',
  },
  {
    title: t('richTextEditor.underline'),
    command: commands.toggleUnderline,
    content: <u className="underline">U</u>,
    isActive: state => isMarkActive(state, schema.marks.underline),
    testId: 'underline',
  },
  {
    title: t('richTextEditor.strikeThrough'),
    command: commands.toggleStrike,
    content: <span className="line-through">S</span>,
    isActive: state => isMarkActive(state, schema.marks.strike),
    testId: 'strike-through',
  },
  'separator',
  {
    title: t('richTextEditor.highlight'),
    command: commands.toggleHighlight,
    content: (
      <span className="relative inline-block">
        <span>A</span>
        <span className="absolute bottom-0 left-0 h-[3px] w-full bg-yellow-200 rounded-sm"></span>
      </span>
    ),
    isActive: state => isMarkActive(state, schema.marks.highlight),
    testId: 'highlight',
  },
  {
    title: t('richTextEditor.superscriptSubscript'),
    command: commands.toggleSupSub,
    testId: 'superscript-subscript',
    content: (state: EditorState) => {
      const hasSuperscript = isMarkActive(state, schema.marks.superscript);
      const hasSubscript = isMarkActive(state, schema.marks.subscript);
      return (
        <span className="text-sm inline-flex items-baseline">
          <span className="text-[0.8em]">A</span>A
          {hasSubscript ? <sub>a</sub> : hasSuperscript ? <sup>a</sup> : ''}
        </span>
      );
    },
    isActive: state =>
      isMarkActive(state, schema.marks.superscript) || isMarkActive(state, schema.marks.subscript),
  },
  'separator',
  {
    title: t('richTextEditor.emoji'),
    command: () => { setShowEmojiPicker(v => !v); return true; },
    testId: 'emoji',
    content: (
      <span role="img" aria-label="emoji">
        <Smile className="w-4 h-4" />
      </span>
    ),
  },
];

const MenuBar: React.FC<MenuBarProps> = ({ editorState, editorView }) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const { t } = useTranslation();

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    if (editorView) {
      editorView.focus();
      if (commands.insertEmoji) {
        commands.insertEmoji(emojiData.emoji)(editorState, editorView.dispatch);
      }
    }
    setShowEmojiPicker(false);
  };

  const executeCommand = (command: Command) => {
    command(editorState, editorView.dispatch);
    editorView.focus();
  };

  return (
    <>
      <div
        data-testid="menu-bar"
        className="flex flex-wrap justify-start items-center py-1 w-full bg-muted mt-2 px-0.5 gap-0.5 rounded-lg z-50 relative"
      >
        {menuItems(setShowEmojiPicker, t).map((item, index) => {
          if (item === 'separator') {
            return <div key={index} className="w-px h-6 bg-tertiary-300 mx-1" />;
          }
          return (
            <MenuItem
              key={index}
              title={item.title}
              command={item.command}
              isActive={!!item.isActive?.(editorState)}
              onExecute={executeCommand}
              data-testid={item.testId}
            >
              {typeof item.content === 'function' ? item.content(editorState) : item.content}
            </MenuItem>
          );
        })}
      </div>
      {showEmojiPicker && (
        <div className="relative top-2 z-50 flex">
          <EmojiPicker
            width={325}
            height={200}
            onEmojiClick={handleEmojiClick}
            skinTonesDisabled={true}
            searchDisabled={true}
            categories={[]}
            autoFocusSearch={false}
            previewConfig={{ showPreview: false }}
          />
        </div>
      )}
    </>
  );
};

export default MenuBar;
