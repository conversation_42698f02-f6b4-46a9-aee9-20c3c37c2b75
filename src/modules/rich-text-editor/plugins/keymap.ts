import {keymap} from 'prosemirror-keymap';
import {history, undo, redo} from 'prosemirror-history';
import {baseKeymap} from 'prosemirror-commands';
import {commands} from '../utils/commands';

export const createKeymap = () => {
  return keymap({
    ...baseKeymap,
    'Mod-z': undo,
    'Mod-y': redo,
    'Mod-Shift-z': redo,
    'Mod-b': commands.toggleBold,
    'Mod-i': commands.toggleItalic,
    'Mod-u': commands.toggleUnderline,
    'Mod-Shift-x': commands.toggleStrike,
    'Shift-Ctrl-.': commands.wrapBlockquote,
    'Mod-Shift-^': commands.toggleSupSub,
    'Mod-Shift-_': commands.toggleSupSub,
    Enter: commands.lineBreakEnter,
    Tab: commands.sinkListItem,
    'Shift-Tab': commands.liftListItem,
  });
};

export const historyPlugin = history();
