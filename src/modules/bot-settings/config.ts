import LanguageGlob from '@/assets/icons/language-glob.svg';
import CannedResponses from '@/assets/icons/canned-responses.svg';
import LLMConfiguration from '@/assets/icons/llm-configuration.svg';
import NLUBrain from '@/assets/icons/nlu-brain.svg';
import Personalization from '@/assets/icons/personalization.svg';
import { TabConfig } from '@/types';

import LanguageSettings from './languageSettings';

export const settingsConfig: (TabConfig & { icon: string; descriptionKey: string })[] = [
  {
    id: 'language',
    labelKey: 'settings.language',
    descriptionKey: 'settings.loremDescription',
    icon: LanguageGlob,
    Component: LanguageSettings,
  },
  {
    id: 'nlu',
    labelKey: 'settings.nlu',
    descriptionKey: 'settings.loremDescription',
    icon: NLUBrain,
  },
  {
    id: 'personalization',
    labelKey: 'settings.personalization',
    descriptionKey: 'settings.loremDescription',
    icon: Personalization,
  },
  {
    id: 'llmConfiguration',
    labelKey: 'settings.llmConfiguration',
    descriptionKey: 'settings.loremDescription',
    icon: LLMConfiguration,
  },
  {
    id: 'cannedResponses',
    labelKey: 'settings.cannedResponses',
    descriptionKey: 'settings.loremDescription',
    icon: CannedResponses,
  },
];
