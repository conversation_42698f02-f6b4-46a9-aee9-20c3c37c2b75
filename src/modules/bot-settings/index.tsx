import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { settingsConfig } from './config';

export default function SettingsGrid() {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const activeSettingId = searchParams.get('tab');

  const activeSetting = useMemo(
    () => settingsConfig.find(setting => setting.id === activeSettingId),
    [activeSettingId]
  );

  const handleTabClick = (id: string) => {
    if (id !== activeSettingId) {
      setSearchParams(prev => {
        const params = new URLSearchParams(prev);
        params.set('tab', id);
        return params;
      });
    }
  };

  if (activeSetting?.Component) {
    const Component = activeSetting.Component;
    return <Component />;
  }

  return (
    <div className="p-6 bg-muted min-h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl">
        {settingsConfig.map(setting => (
          <div
            key={setting.id}
            className="bg-card rounded-lg border border-secondary-300 p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleTabClick(setting.id)}
          >
            <div className="flex items-start justify-between space-x-4">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium mb-2 text-base">{t(setting.labelKey)}</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {t(setting.descriptionKey)}
                </p>
              </div>
              <div className="w-10 h-10 flex-shrink-0">
                <img
                  src={setting.icon}
                  alt={setting.labelKey}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
