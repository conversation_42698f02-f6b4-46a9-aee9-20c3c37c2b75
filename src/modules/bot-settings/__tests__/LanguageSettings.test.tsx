/// <reference types="vitest/globals" />
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import LanguageSettings from '../languageSettings';
import * as languageApi from '@/store/api/languageApi';
import * as useRouterParam from '@/hooks/useRouterParam';
import * as useToast from '@/hooks/use-toast';
import { Language, OrderDirection } from '@/types';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock react-router-dom's useSearchParams
vi.mock('react-router-dom', async importActual => {
  const actual = await importActual();
  return {
    ...(actual as object),
    useSearchParams: vi.fn(),
  };
});

// Mock useRouterParam
vi.mock('@/hooks/useRouterParam', () => ({
  useBotIdParam: vi.fn(() => ({ botId: 'mockBotId' })),
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

// Mock API hooks
vi.mock('@/store/api/languageApi', () => ({
  useGetLanguagesQuery: vi.fn(),
  useGetBotLanguagesQuery: vi.fn(),
  useCreateBulkBotLanguagesMutation: vi.fn(),
  useDeleteBulkBotLanguagesMutation: vi.fn(),
}));

const mockAllLanguages: Language[] = [
  { id: 'lang1', name: 'English', nativeName: 'English', code: 'en', createdAt: '' },
  { id: 'lang2', name: 'Spanish', nativeName: 'Español', code: 'es', createdAt: '' },
  { id: 'lang3', name: 'French', nativeName: 'Français', code: 'fr', createdAt: '' },
];

const mockBotLanguages = [
  { id: 'botLang1', langId: 'lang1', botId: 'mockBotId', isDefault: true, createdAt: '' },
];

describe('LanguageSettings', () => {
  const mockSetSearchParams = vi.fn();
  const mockCreateBulkBotLanguages = vi.fn();
  const mockDeleteBulkBotLanguages = vi.fn();

  beforeEach(() => {
    (useSearchParams as any).mockReturnValue([new URLSearchParams(), mockSetSearchParams]);
    (languageApi.useGetLanguagesQuery as any).mockReturnValue({
      data: { data: { items: mockAllLanguages } },
      isLoading: false,
    });
    (languageApi.useGetBotLanguagesQuery as any).mockReturnValue({
      data: { data: { items: mockBotLanguages } },
      isLoading: false,
    });
    (languageApi.useCreateBulkBotLanguagesMutation as any).mockReturnValue([
      mockCreateBulkBotLanguages,
      { isLoading: false },
    ]);
    (languageApi.useDeleteBulkBotLanguagesMutation as any).mockReturnValue([
      mockDeleteBulkBotLanguages,
      { isLoading: false },
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with initial languages', () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    expect(screen.getByText('settings.languages')).toBeInTheDocument();
    expect(screen.getByText('settings.yourLanguages')).toBeInTheDocument();
    expect(screen.getByText('settings.availableLanguages')).toBeInTheDocument();
    expect(screen.getByText('English')).toBeInTheDocument(); // From selected languages
    expect(screen.getByText('Spanish')).toBeInTheDocument(); // From available languages
  });

  it('handles adding a language', async () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    const spanishItem = screen.getByText('Spanish');
    fireEvent.click(spanishItem);

    await waitFor(() => {
      expect(screen.getAllByText('Spanish')[0]).toBeInTheDocument(); // Should now be in selected languages
    });
  });

  it('handles removing a language', async () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    const englishItem = screen.getByText('English');
    const removeButton = englishItem.closest('.flex')?.querySelector('svg'); // Assuming X icon is SVG
    if (removeButton) {
      fireEvent.click(removeButton);
    }

    await waitFor(() => {
      expect(screen.queryByText('English')).not.toBeInTheDocument(); // Should be removed from selected languages
    });
  });

  it('handles search term change', () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    const searchInput = screen.getByPlaceholderText('settings.searchLanguages');
    fireEvent.change(searchInput, { target: { value: 'fr' } });

    expect(screen.getByText('French')).toBeInTheDocument();
    expect(screen.queryByText('Spanish')).not.toBeInTheDocument();
  });

  it('calls createBulkBotLanguages and deleteBulkBotLanguages on save', async () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    // Add French
    fireEvent.click(screen.getByText('French'));

    // Remove English
    const englishItem = screen.getByText('English');
    const removeButton = englishItem.closest('.flex')?.querySelector('svg');
    if (removeButton) {
      fireEvent.click(removeButton);
    }

    fireEvent.click(screen.getByText('common.save'));

    await waitFor(() => {
      expect(mockCreateBulkBotLanguages).toHaveBeenCalledWith({
        botId: 'mockBotId',
        ids: ['lang3'],
      });
      expect(mockDeleteBulkBotLanguages).toHaveBeenCalledWith({
        botId: 'mockBotId',
        ids: ['botLang1'],
      });
      expect(useToast.toast).toHaveBeenCalledWith({
        title: expect.any(Object),
      });
    });
  });

  it('does not call API if no changes on save', async () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    fireEvent.click(screen.getByText('common.save'));

    await waitFor(() => {
      expect(mockCreateBulkBotLanguages).not.toHaveBeenCalled();
      expect(mockDeleteBulkBotLanguages).not.toHaveBeenCalled();
      expect(useToast.toast).toHaveBeenCalledWith({
        title: expect.any(Object),
      });
    });
  });

  it('handles go back button click', () => {
    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    fireEvent.click(screen.getByText('common.goBack'));

    expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
    const setSearchParamsCallback = mockSetSearchParams.mock.calls[0][0];
    const prevSearchParams = new URLSearchParams('tab=language');
    const newSearchParams = setSearchParamsCallback(prevSearchParams);
    expect(newSearchParams.get('tab')).toBeNull();
  });

  it('shows loading state for all languages', () => {
    (languageApi.useGetLanguagesQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    expect(screen.queryByText('settings.availableLanguages')).toBeInTheDocument(); // Component still renders, but languages won't be there
    expect(screen.queryByText('English')).not.toBeInTheDocument();
  });

  it('shows loading state for bot languages', () => {
    (languageApi.useGetBotLanguagesQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    render(
      <BrowserRouter>
        <LanguageSettings />
      </BrowserRouter>
    );

    expect(screen.queryByText('settings.yourLanguages')).toBeInTheDocument(); // Component still renders, but languages won't be there
    expect(screen.queryByText('English')).not.toBeInTheDocument();
  });
});
