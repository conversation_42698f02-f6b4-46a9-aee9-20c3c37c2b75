/// <reference types="vitest/globals" />
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import LanguageSettings from '../languageSettings';
import * as languageApi from '@/store/api/languageApi';
import * as useRouterParam from '@/hooks/useRouterParam';
import * as useToast from '@/hooks/use-toast';
import { Language, OrderDirection } from '@/types';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock react-router-dom's useSearchParams
vi.mock('react-router-dom', async importActual => {
  const actual = await importActual();
  return {
    ...(actual as object),
    useSearchParams: vi.fn(),
  };
});

// Mock useRouterParam
vi.mock('@/hooks/useRouterParam', () => ({
  useBotIdParam: vi.fn(() => ({ botId: 'mockBotId' })),
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

// Mock API hooks
vi.mock('@/store/api/languageApi', () => ({
  useGetLanguagesQuery: vi.fn(),
  useGetBotLanguagesQuery: vi.fn(),
  useCreateBulkBotLanguagesMutation: vi.fn(),
  useDeleteBulkBotLanguagesMutation: vi.fn(),
}));

const mockAllLanguages: Language[] = [
  { id: 'lang1', name: 'English', nativeName: 'English', code: 'en', createdAt: '' },
  { id: 'lang2', name: 'Spanish', nativeName: 'Español', code: 'es', createdAt: '' },
  { id: 'lang3', name: 'French', nativeName: 'Français', code: 'fr', createdAt: '' },
];

const mockBotLanguages = [
  { id: 'botLang1', langId: 'lang1', botId: 'mockBotId', isDefault: true, createdAt: '' },
];
