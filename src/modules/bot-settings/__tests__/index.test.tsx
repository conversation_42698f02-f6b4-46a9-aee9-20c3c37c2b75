/// <reference types="vitest/globals" />
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import SettingsGrid from '../index';
import { settingsConfig } from '../config';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Simple mock: returns the key itself
  }),
}));

// Mock react-router-dom's useSearchParams
vi.mock('react-router-dom', async importActual => {
  const actual = await importActual();
  return {
    ...(actual as object),
    useSearchParams: vi.fn(),
  };
});

// Mock LanguageSettings component
vi.mock('../languageSettings/index', () => ({
  default: () => <div data-testid="language-settings-mock" />,
}));

describe('SettingsGrid', () => {
  const mockSetSearchParams = vi.fn();

  beforeEach(() => {
    (useSearchParams as any).mockReturnValue([new URLSearchParams(), mockSetSearchParams]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the settings grid correctly', () => {
    render(
      <BrowserRouter>
        <SettingsGrid />
      </BrowserRouter>
    );

    settingsConfig.forEach(setting => {
      expect(screen.getByText(setting.labelKey)).toBeInTheDocument();
      expect(screen.getAllByText(setting.descriptionKey)[0]).toBeInTheDocument();
    });
  });

  it('navigates to LanguageSettings when language tab is clicked', () => {
    render(
      <BrowserRouter>
        <SettingsGrid />
      </BrowserRouter>
    );

    const languageSetting = screen.getByText('settings.language');
    fireEvent.click(languageSetting);

    expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
    // Verify that the function passed to setSearchParams correctly sets the 'tab' parameter
    const setSearchParamsCallback = mockSetSearchParams.mock.calls[0][0];
    const prevSearchParams = new URLSearchParams();
    const newSearchParams = setSearchParamsCallback(prevSearchParams);
    expect(newSearchParams.get('tab')).toBe('language');
  });

  it('renders LanguageSettings component when "tab" search param is "language"', () => {
    (useSearchParams as any).mockReturnValue([
      new URLSearchParams('tab=language'),
      mockSetSearchParams,
    ]);

    render(
      <BrowserRouter>
        <SettingsGrid />
      </BrowserRouter>
    );

    expect(screen.getByTestId('language-settings-mock')).toBeInTheDocument();
    settingsConfig.forEach(setting => {
      if (setting.id !== 'language') {
        expect(screen.queryByText(setting.labelKey)).not.toBeInTheDocument();
      }
    });
  });

  it('does not navigate if the same tab is clicked', () => {
    (useSearchParams as any).mockReturnValue([
      new URLSearchParams('tab=language'),
      mockSetSearchParams,
    ]);

    render(
      <BrowserRouter>
        <SettingsGrid />
      </BrowserRouter>
    );

    const languageSetting = screen.getByText('settings.language');
    fireEvent.click(languageSetting);

    expect(mockSetSearchParams).not.toHaveBeenCalled();
  });

  it('navigates to other settings when their tab is clicked', () => {
    render(
      <BrowserRouter>
        <SettingsGrid />
      </BrowserRouter>
    );

    const nluSetting = screen.getByText('settings.nlu');
    fireEvent.click(nluSetting);

    expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
    const setSearchParamsCallback = mockSetSearchParams.mock.calls[0][0];
    const prevSearchParams = new URLSearchParams();
    const newSearchParams = setSearchParamsCallback(prevSearchParams);
    expect(newSearchParams.get('tab')).toBe('nlu');
  });

  it('renders the settings grid when an unknown tab is in the search params', () => {
    (useSearchParams as any).mockReturnValue([
      new URLSearchParams('tab=unknown'),
      mockSetSearchParams,
    ]);

    render(
      <BrowserRouter>
        <SettingsGrid />
      </BrowserRouter>
    );

    settingsConfig.forEach(setting => {
      expect(screen.getByText(setting.labelKey)).toBeInTheDocument();
    });
    expect(screen.queryByTestId('language-settings-mock')).not.toBeInTheDocument();
  });
});
