import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Search } from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Language } from '@/types';
import LanguageItem from './LanguageItem';
import { LanguageNode } from '../types';

interface AvailableLanguagesProps {
  allLanguages: Language[];
  selectedLanguages: LanguageNode[];
  searchTerm: string;
  onAddLanguage: (language: LanguageNode) => void;
  onSearchTermChange: (term: string) => void;
}

const AvailableLanguages: React.FC<AvailableLanguagesProps> = ({
  allLanguages,
  selectedLanguages,
  searchTerm,
  onAddLanguage,
  onSearchTermChange,
}) => {
  const { t } = useTranslation();

  const availableLanguages = useMemo(() => {
    return allLanguages || [];
  }, [allLanguages]);

  const filteredAvailableLanguages = useMemo(() => {
    return availableLanguages.filter(
      lang =>
        lang.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lang.nativeName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [availableLanguages, searchTerm]);

  const groupedAvailableLanguages = useMemo(() => {
    return filteredAvailableLanguages.reduce(
      (acc, lang) => {
        const firstLetter = lang.name.charAt(0).toUpperCase();
        if (!acc[firstLetter]) {
          acc[firstLetter] = [];
        }
        acc[firstLetter].push(lang);
        return acc;
      },
      {} as Record<string, Language[]>
    );
  }, [filteredAvailableLanguages]);

  return (
    <div className="rounded-lg p-6">
      <h3 className="text-lg text-foreground mb-4">
        {t('settings.availableLanguages')}
      </h3>
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          placeholder={t('settings.searchLanguages')}
          className="pl-9 pr-3 py-2 border border-secondary-300 rounded-md w-full"
          value={searchTerm}
          onChange={e => onSearchTermChange(e.target.value)}
        />
      </div>

      {Object.keys(groupedAvailableLanguages)
        .sort()
        .map(letter => (
          <div key={letter} className="mb-6">
            <div className="flex items-center justify-between mb-4 gap-3">
              <h4 className="text-base font-semibold text-tertiary-300">{letter}</h4>
              <hr className="border-t border-secondary-300 flex-1" />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {groupedAvailableLanguages[letter].map(lang => (
                <LanguageItem
                  key={lang.id}
                  language={lang}
                  onSelect={onAddLanguage}
                  isSelected={selectedLanguages.some(sLang => sLang.id === lang.id)}
                />
              ))}
            </div>
          </div>
        ))}
    </div>
  );
};

export default AvailableLanguages;
