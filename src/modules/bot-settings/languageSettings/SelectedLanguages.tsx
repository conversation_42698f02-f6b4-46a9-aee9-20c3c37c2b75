import React from 'react';
import { useTranslation } from 'react-i18next';

import { LanguageNode } from '../types';
import LanguageItem from './LanguageItem';

interface SelectedLanguagesProps {
  selectedLanguages: LanguageNode[];
  onRemoveLanguage: (language: LanguageNode) => void;
}

const SelectedLanguages: React.FC<SelectedLanguagesProps> = ({
  selectedLanguages,
  onRemoveLanguage,
}) => {
  const { t } = useTranslation();

  return (
    <div className="rounded-lg p-6">
      <div className="flex flex-col justify-between gap-2 mb-4">
        <h3 className="text-foreground">{t('settings.yourLanguages')}</h3>
        <p className="text-muted-foreground text-sm">{t('settings.allSelectedLanguages')}</p>
      </div>
      {selectedLanguages.length === 0 ? (
        <p className="text-muted-foreground">{t('settings.noLanguagesSelected')}</p>
      ) : (
        <div className="flex flex-wrap gap-3">
          {selectedLanguages.map(lang => (
            <LanguageItem
              key={lang.id}
              language={lang}
              isRemovable={true}
              onRemove={onRemoveLanguage}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default SelectedLanguages;
