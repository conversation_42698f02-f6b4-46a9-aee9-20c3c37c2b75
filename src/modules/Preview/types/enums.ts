export enum SenderType {
  USER = 'user',
  BOT = 'bot',
}

export enum RendererType {
  USER_MESSAGE = 'user_message',
  MESSAGE = 'message',
  FORM = 'form',
  FEEDBACK = 'feedback',
}

export enum TimeFormat {
  TWELVE_HOUR = '12',
  TWENTY_FOUR_HOUR = '24',
}

export enum Meridian {
  AM = 'AM',
  PM = 'PM',
}

export enum FormType {
  SINGLE_ASK_FORM = 'single-ask-form',
  MULTI_ASK_FORM = 'multi-ask-form',
}

export enum PlatformType {
  Web = 'web',
  Mobile = 'mobile',
}

export enum FormFieldType {
  TEXT = 'text',
  TEXT_FIELD = 'text_field',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  TIME = 'time',
  DATE = 'date',
  PAST_DATE = 'past_date',
  FUTURE_DATE = 'future_date',
  CUSTOM_DATE = 'custom_date',
  MOBILE_NUMBER = 'mobile_number',
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
}
