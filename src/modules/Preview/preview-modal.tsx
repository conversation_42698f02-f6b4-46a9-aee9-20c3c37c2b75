'use client';
import React, { useState, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { togglePreview } from '@/store/slices/uiSlice';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import PreviewHeader from './previewHeader';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import ConfirmDialog from './ConfirmDialog';
import { PlatformType, FormType, RendererType, BotFormFields, ChatMessage, FormMessage, FeedbackMessage } from './types';
import { useSendPreviewMessageMutation, useResetConversationMutation } from '@/store/api';
import DebuggerPanel from './debugger';
import { SendMessageRequest } from '@/types/botInteraction.type';
import {
  createUserMessage,
  createFormSubmissionPayload,
  createTextMessagePayload,
  createSingleFieldFormPayload,
  processServerResponse,
  isSingleTextFieldForm,
  hasBlockingForm,
} from './utils/messageUtils';

const PreviewModal: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { showPreview } = useAppSelector(state => state.ui);
  const [showDebugger, setShowDebugger] = useState(false);
  const { botId } = useBotIdParam();
  const [platform, setPlatform] = useState<PlatformType>(PlatformType.Web);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sendPreviewMessageApi, { isLoading: isSendingMessage }] = useSendPreviewMessageMutation();
  const [resetConversationApi, { isLoading: isResettingConversation }] =
    useResetConversationMutation();
  const [error, setError] = useState<string | null>(null);
  const [lastFormPrompt, setLastFormPrompt] = useState<ChatMessage | null>(null);
  const [lastFormFieldValues, setLastFormFieldValues] = useState<BotFormFields | undefined>(
    undefined
  );
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formLocked, setFormLocked] = useState(false);
  const [isFormActive, setIsFormActive] = useState(false);
  const [isFeedbackForm, setIsFeedbackForm] = useState(false);
  const conversationIdRef = useRef<string>(crypto.randomUUID());

  const isLoading = isSendingMessage || isResettingConversation;

  const chatForm = useForm<{ message: string }>({
    defaultValues: { message: '' },
  });
  const { setValue } = chatForm;

  const isFormValid = useCallback((): boolean => {
    if (!lastFormPrompt) return false;
    if (isFeedbackForm) {
      return Boolean(lastFormFieldValues?.feedback);
    }
    // Type guard to ensure we have FormData
    if (lastFormPrompt.nodeType === RendererType.FORM && 'prompt' in lastFormPrompt.data) {
      return lastFormPrompt.data.prompt.every(
        (field: any) =>
          !field.required || (lastFormFieldValues && lastFormFieldValues[field.fieldName])
      );
    }
    return false;
  }, [lastFormPrompt, lastFormFieldValues]);

  const sendMessageToServer = useCallback(
    async (body: SendMessageRequest) => {
      setError(null);
      try {
        const data = await sendPreviewMessageApi({
          id: conversationIdRef.current,
          body,
        }).unwrap();

        const botMessages = processServerResponse(
          (data as any)?.data?.response || (data as any)?.response || []
        );
        setMessages(prev => [...prev, ...botMessages]);

        const formMessage = botMessages.find(m => m.nodeType === RendererType.FORM);
        const feedbackMessage = botMessages.find(m => m.nodeType === RendererType.FEEDBACK);

        if (formMessage) {
          setLastFormPrompt(formMessage);
          const initial: BotFormFields = {};
          formMessage.data.prompt.forEach(field => (initial[field.fieldName] = ''));
          setLastFormFieldValues(initial);
        } else if (feedbackMessage) {
          setLastFormPrompt(feedbackMessage);
          setLastFormFieldValues({ feedback: '' });
          setIsFeedbackForm(true);
        } else {
          setLastFormPrompt(null);
          setLastFormFieldValues(undefined);
        }
      } catch (err: any) {
        setError(err.message || t('errors.somethingWrong'));
      } finally {
        setFormLocked(false);
      }
    },
    [botId, t, sendPreviewMessageApi]
  );

  const handleChatInputSubmit = useCallback(
    async (fields: { message: string }) => {
      if (isSendingMessage) return;

      const userInput = fields.message.trim();
      if (!userInput) return;

      const isSingleTextField = isSingleTextFieldForm(lastFormPrompt);

      if (lastFormPrompt && isSingleTextField) {
        const fieldName = lastFormPrompt.data.prompt[0].fieldName;

        setMessages(prev => [...prev, createUserMessage(userInput)]);

        const formPayload = createSingleFieldFormPayload(
          fieldName,
          userInput,
          botId,
          conversationIdRef.current,
          lastFormPrompt.data.formId
        );

        await sendMessageToServer(formPayload);
        chatForm.reset({ message: '' });
        setValue('message', '');
        return;
      }

      if (hasBlockingForm(lastFormPrompt)) {
        return; // multi-field form → block sending
      }

      setMessages(prev => [...prev, createUserMessage(userInput)]);

      const textPayload = createTextMessagePayload(userInput, botId, conversationIdRef.current);
      await sendMessageToServer(textPayload);

      chatForm.reset({ message: '' });
      setValue('message', '');
    },
    [botId, isSendingMessage, sendMessageToServer, chatForm, lastFormPrompt, setValue]
  );


const handleFormPromptSubmit = useCallback(
    async (fields: BotFormFields) => {
      if (!lastFormPrompt || isSendingMessage) return;

      const formPayload = createFormSubmissionPayload(
        fields,
        botId,
        conversationIdRef.current,

        lastFormPrompt.nodeType === RendererType.FORM ? lastFormPrompt.data?.formId : undefined
      );

      setMessages(prev => {
        if (!lastFormPrompt || !formPayload.formData) {
          return prev; // Should not happen if logic is correct, but for safety
        }

        const lastFormPromptIndex = prev.indexOf(lastFormPrompt);

        if (lastFormPromptIndex === -1) {
          return prev; // lastFormPrompt not found in prev array, return as is
        }

        // Assert lastFormPrompt to be a FormMessage or FeedbackMessage
        const currentFormPrompt = lastFormPrompt as FormMessage | FeedbackMessage;

        const updatedData = {
          ...currentFormPrompt.data,
          submittedValues: formPayload.formData,
        };

        const updatedLastFormPrompt = {
          ...currentFormPrompt,
          data: updatedData,
        } as ChatMessage;

        const newMessages = [...prev];
        newMessages[lastFormPromptIndex] = updatedLastFormPrompt;
        return newMessages;
      });

      setLastFormFieldValues(fields);
      setFormLocked(true);

      await sendMessageToServer(formPayload);
    },
    [lastFormPrompt, isSendingMessage, sendMessageToServer, botId]
  );


  const resetConversation = useCallback(async () => {
    try {
      await resetConversationApi({ conversationId: conversationIdRef.current }).unwrap();
    } catch (err) {
      console.error('Reset failed:', err);
    }
  }, [resetConversationApi]);

  const clearState = useCallback(() => {
    setMessages([]);
    setValue('message', '');
    setLastFormPrompt(null);
    setLastFormFieldValues(undefined);
    setFormLocked(false);
    setError(null);
    conversationIdRef.current = crypto.randomUUID();
    dispatch(togglePreview());
  }, [dispatch]);

  const handleClose = useCallback(() => {
    if (messages.length > 0) setShowConfirmDialog(true);
    else clearState();
  }, [messages, clearState]);

  const confirmClose = useCallback(
    async (confirm: boolean) => {
      setShowConfirmDialog(false);
      if (confirm) {
        await resetConversation();
        clearState();
      }
    },
    [resetConversation, clearState]
  );

  const isSingleTextField =
    isSingleTextFieldForm(lastFormPrompt) &&
    (lastFormPrompt?.data?.formType === FormType.SINGLE_ASK_FORM ||
      lastFormPrompt?.data?.formType === FormType.MULTI_ASK_FORM);

  if (!showPreview) return null;

  return (
    <div className="fixed right-0 top-0 bottom-0 border bg-background flex items-center justify-center z-50">
      <div className="w-96 h-full flex flex-col">
        <PreviewHeader
          platform={platform}
          setPlatform={setPlatform}
          onDebuggerToggle={() => setShowDebugger(!showDebugger)}
          onClose={handleClose}
        />
        <div className="flex-1 p-4 overflow-auto">
          <div className="bg-background rounded-lg h-full flex flex-col">
            <ChatMessages
              messages={messages}
              lastFormPrompt={lastFormPrompt}
              formLocked={formLocked}
              onFormSubmit={handleFormPromptSubmit}
              lastFormFieldValues={lastFormFieldValues}
              loading={isLoading}
              onFormActiveChange={setIsFormActive}
            />
            {error && <div className="text-error-500 text-xs mt-2">{error}</div>}
            <ChatInput
              activeForm={lastFormPrompt}
              loading={isLoading}
              isFormValid={isFormValid}
              disabled={isFormActive}
              isSingleTextField={!!isSingleTextField}
              onSubmit={handleChatInputSubmit}
              form={chatForm}
            />
          </div>
        </div>
        <ConfirmDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
          onConfirm={confirmClose}
        />
      </div>
      {showDebugger && (
        <DebuggerPanel conversationId={conversationIdRef.current} onOpenChange={setShowDebugger} />
      )}
    </div>
  );
};

export default PreviewModal;
