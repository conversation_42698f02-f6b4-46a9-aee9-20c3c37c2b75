import React from 'react';
import { DatePicker } from '@/components/DatePicker/date-picker';
import TimePicker from '@/components/time-picker';
import { Input } from '@/components/ui/input';
import { FormFieldType, TimeFormat } from './types/enums';

export interface FieldRendererProps {
  formLocked: boolean;
  value?: any;
  onChange: (value: any) => void;
  className?: string;
  fieldName?: string;
  placeholder?: string;
  selected?: Date;
  onSelect?: (date: any) => void;
  fieldType?: FormFieldType;
  customRange?: { from: Date; to: Date };
}

export type FieldRenderer = (props: FieldRendererProps) => React.ReactElement;

export const FIELD_RENDERERS: Record<string, FieldRenderer> = {
  time: (props) => (
    <TimePicker
      disabled={props.formLocked}
      value={props.value}
      onChange={props.onChange}
      className={props.className}
      format={TimeFormat.TWELVE_HOUR}
    />
  ),
  date: (props) => (
    <DatePicker
      disabled={props.formLocked}
      selected={props.selected}
      onSelect={props.onSelect as any}
      fieldType={props.fieldType as any}
      customRange={props.customRange}
      className={props.className}
    />
  ),
  email: (props) => (
    <Input
      type="email"
      name={props.fieldName}
      placeholder={props.placeholder}
      value={props.value}
      onChange={props.onChange}
      disabled={props.formLocked}
      className={props.className}
    />
  ),
  text: (props) => (
    <Input
      type="text"
      name={props.fieldName}
      placeholder={props.placeholder}
      value={props.value}
      onChange={props.onChange}
      disabled={props.formLocked}
      className={props.className}
    />
  ),
};

/**
 * Registers a new field renderer
 */
export const registerFieldRenderer = (fieldType: string, renderer: FieldRenderer): void => {
  FIELD_RENDERERS[fieldType] = renderer;
};

/**
 * Gets a field renderer by type
 */
export const getFieldRenderer = (fieldType: string): FieldRenderer | null => {
  return FIELD_RENDERERS[fieldType] || null;
};
