import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { AlertTriangle, AlertCircle, Info } from 'lucide-react';
import { DebuggerEvent, DebuggerEventType, LogLevel } from '@/types/botInteraction.type';

interface LogsProps {
  logs: DebuggerEvent[];
}

const logBackgroundClasses = {
  [LogLevel.ERROR]: 'bg-error-50',
  [LogLevel.WARNING]: 'bg-warning-50',
  [LogLevel.INFO]: 'bg-secondary-50',
};

const logIconClasses = {
  [LogLevel.ERROR]: 'text-error-500',
  [LogLevel.WARNING]: 'text-warning-500',
  [LogLevel.INFO]: 'text-primary-500',
};

const logTextClasses = {
  [LogLevel.ERROR]: 'text-error-700',
  [LogLevel.WARNING]: 'text-warning-700',
  [LogLevel.INFO]: 'text-primary-700',
};

const logIcons = {
  [LogLevel.ERROR]: AlertCircle,
  [LogLevel.WARNING]: AlertTriangle,
  [LogLevel.INFO]: Info,
};

const fallbackLogLevel = LogLevel.INFO;

const Logs: React.FC<LogsProps> = ({ logs }) => {
  const { t } = useTranslation();

  const filteredLogs = logs.filter(log => log.type === DebuggerEventType.LOG);

  return (
    <div className="flex-1 flex flex-col gap-2">
      {filteredLogs.length === 0 ? (
        <div className="flex items-center justify-center p-6 bg-muted h-full text-secondary-500">
          <p>{t('debugger.noLogs')}</p>
        </div>
      ) : (
        filteredLogs.map((log, index) => {
          const logPayload = log.payload;
          const message = logPayload.message;
          const type = logPayload.level;
          const IconComponent = logIcons[type] || logIcons[fallbackLogLevel];

          return (
            <div
              key={index}
              className={cn(
                'flex items-start space-x-3 px-4 py-3 border-b border-secondary-100',
                logBackgroundClasses[type] || logBackgroundClasses[fallbackLogLevel]
              )}
            >
              <IconComponent
                className={cn(
                  'w-4 h-4 mt-0.5 flex-shrink-0',
                  logIconClasses[type] || logIconClasses[fallbackLogLevel]
                )}
              />
              <div className="flex-1 min-w-0">
                <div
                  className={cn(
                    'text-sm font-mono',
                    logTextClasses[type] || logTextClasses[fallbackLogLevel]
                  )}
                >
                  {message}
                </div>
              </div>
              <button className="text-secondary-400 hover:text-secondary-600 flex-shrink-0">
                <Info className="w-4 h-4" />
              </button>
            </div>
          );
        })
      )}
    </div>
  );
};

export default Logs;
