import z from 'zod';
import { FormFieldData } from './types/types';
import { FormFieldType } from './types/enums';
import { format } from 'date-fns';
import { PhoneNumberUtil } from 'google-libphonenumber';
import i18n from 'i18next';

const phoneUtil = PhoneNumberUtil.getInstance();

export const createPhoneSchema = (field: FormFieldData) =>
  z
    .string()
    .optional()
    .refine(
      val => {
        if (!val || val.trim() === '') return !field.required;

        try {
          const regionCode = phoneUtil.getRegionCodeForNumber(phoneUtil.parse(val));

          const parsed = phoneUtil.parseAndKeepRawInput(val, regionCode);

          return phoneUtil.isValidNumberForRegion(parsed, regionCode);
        } catch (err) {
          console.warn('Phone validation failed', err);
          return false;
        }
      },
      {
        message: i18n.t('validation.invalidPhoneNumber', { field: field.label || field.fieldName }),
      }
    );

const isValidDate = (value: string | Date): boolean => {
  const date = value instanceof Date ? value : new Date(value);
  return !isNaN(date.getTime()) && date.toString() !== 'Invalid Date';
};

const withRequired = <T extends z.ZodTypeAny>(schema: T, field: FormFieldData, message: string) =>
  field.required
    ? schema.refine(val => val !== undefined && val !== '', {
        message: i18n.t('validation.fieldRequired', { field: message }),
      })
    : schema.optional();

export const createTextFieldSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .trim()
      .min(1, {
        message: i18n.t('validation.fieldRequired', { field: field.label || field.fieldName }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  );

export const createEmailSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .email({
        message: i18n.t('validation.invalidEmail', { field: field.label || field.fieldName }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  );

export const createPasswordSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .min(6, {
        message: i18n.t('validation.passwordMinLength', { field: field.label || field.fieldName }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  );

export const createTimeSchema = (field: FormFieldData) => {
  const timeRegex24 = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  const timeRegex12 = /^(1[0-2]|0?[1-9]):[0-5][0-9]\s?(AM|PM|am|pm)?$/;
  const isValidTime = (val: string) => timeRegex24.test(val) || timeRegex12.test(val);

  return withRequired(
    z
      .string()
      .refine(val => (!field.required ? val === '' || isValidTime(val) : isValidTime(val)), {
        message: i18n.t('validation.invalidTimeFormat', { field: field.label || field.fieldName }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  );
};

export const createPastDateSchema = (field: FormFieldData) => {
  const currentDate = new Date('2025-08-03T11:37:00Z');

  return withRequired(
    z
      .union([z.string(), z.date()])
      .refine(isValidDate, {
        message: i18n.t('validation.invalidDate', { field: field.label || field.fieldName }),
      })
      .transform(val => (typeof val === 'string' ? new Date(val) : val))
      .refine(val => !val || val < currentDate, {
        message: i18n.t('validation.pastDateRequired', {
          field: field.label || field.fieldName,
          date: format(currentDate, 'PPP p'),
        }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  );
};

export const createFutureDateSchema = (field: FormFieldData) => {
  const currentDate = new Date('2025-08-03T11:37:00Z');

  return withRequired(
    z
      .union([z.string(), z.date()])
      .refine(isValidDate, {
        message: i18n.t('validation.invalidDate', { field: field.label || field.fieldName }),
      })
      .transform(val => (typeof val === 'string' ? new Date(val) : val))
      .refine(val => !val || val > currentDate, {
        message: i18n.t('validation.futureDateRequired', {
          field: field.label || field.fieldName,
          date: format(currentDate, 'PPP p'),
        }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  );
};
// export const createCustomDateSchema = (field: FormField) => {
//   const rangeStart = field.rangeStart
//     ? isValidDate(field.rangeStart)
//       ? new Date(field.rangeStart)
//       : new Date(0)
//     : new Date(0);
//   const rangeEnd = field.rangeEnd
//     ? isValidDate(field.rangeEnd)
//       ? new Date(field.rangeEnd)
//       : new Date('2025-08-03T11:37:00Z')
//     : new Date('2025-08-03T11:37:00Z');

//   return withRequired(
//     z
//       .object({
//         start: z
//           .union([z.string(), z.date()])
//           .optional()
//           .transform(val => (typeof val === 'string' ? new Date(val) : val))
//           .refine(val => !val || isValidDate(val), {
//             message: `${field.label || field.fieldName}.start must be a valid date`,
//           }),
//         end: z
//           .union([z.string(), z.date()])
//           .optional()
//           .transform(val => (typeof val === 'string' ? new Date(val) : val))
//           .refine(val => !val || isValidDate(val), {
//             message: `${field.label || field.fieldName}.end must be a valid date`,
//           }),
//       })
//       .superRefine((data, ctx) => {
//         const { start, end } = data;

//         if (field.required && (!start || !end)) {
//           ctx.addIssue({
//             code: 'custom',
//             message: `${field.label || field.fieldName} is required`,
//           });
//           return;
//         }

//         if (!start || !end) return;

//         if (isNaN(start.getTime())) {
//           ctx.addIssue({
//             path: ['start'],
//             code: 'custom',
//             message: `${field.label || field.fieldName}.start must be a valid date`,
//           });
//         }

//         if (isNaN(end.getTime())) {
//           ctx.addIssue({
//             path: ['end'],
//             code: 'custom',
//             message: `${field.label || field.fieldName}.end must be a valid date`,
//           });
//         }

//         if (start > end) {
//           ctx.addIssue({
//             code: 'custom',
//             message: `${field.label || field.fieldName} end date must be after start date`,
//           });
//         }

//         if (start < rangeStart || end > rangeEnd) {
//           ctx.addIssue({
//             code: 'custom',
//             message: `${field.label || field.fieldName} must be between ${format(
//               rangeStart,
//               'PPP'
//             )} and ${format(rangeEnd, 'PPP')}`,
//           });
//         }
//       }),
//     field,
//     `${field.label || field.fieldName} is required`
//   );
// };

export const createNumberSchema = (field: FormFieldData) => {
  const base = z
    .string()
    .refine(val => val === '' || !isNaN(Number(val)), {
      message: i18n.t('validation.invalidNumber', { field: field.label || field.fieldName }),
    })
    .transform(val => (val === '' ? undefined : Number(val)))
    .refine(val => val === undefined || typeof val === 'number', {
      message: i18n.t('validation.invalidNumber', { field: field.label || field.fieldName }),
    })
    .refine(val => val === undefined || !isNaN(val), {
      message: i18n.t('validation.invalidNumber', { field: field.label || field.fieldName }),
    });

  return withRequired(
    base,
    field,
    i18n.t('validation.fieldRequired', { field: field.label || field.fieldName })
  ).optional();
};

export const createFormValidationSchema = (fields: FormFieldData[]) =>
  z.object(
    fields.reduce(
      (schema, field) => {
        switch (field.fieldType) {
          case FormFieldType.TEXT:
          case FormFieldType.TEXT_FIELD:
            schema[field.fieldName] = createTextFieldSchema(field);
            break;
          case FormFieldType.EMAIL:
            schema[field.fieldName] = createEmailSchema(field);
            break;
          case FormFieldType.PASSWORD:
            schema[field.fieldName] = createPasswordSchema(field);
            break;
          case FormFieldType.NUMBER:
            schema[field.fieldName] = createNumberSchema(field);
            break;
          case FormFieldType.TIME:
            schema[field.fieldName] = createTimeSchema(field);
            break;
          case FormFieldType.PAST_DATE:
            schema[field.fieldName] = createPastDateSchema(field);
            break;
          case FormFieldType.FUTURE_DATE:
            schema[field.fieldName] = createFutureDateSchema(field);
            break;
          case FormFieldType.MOBILE_NUMBER:
            schema[field.fieldName] = createPhoneSchema(field);
            break;
          default:
            schema[field.fieldName] = z.string().optional();
        }
        return schema;
      },
      {} as Record<string, z.ZodTypeAny>
    )
  );
