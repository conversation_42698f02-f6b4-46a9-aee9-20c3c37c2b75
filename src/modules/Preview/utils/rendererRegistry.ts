import React from 'react';
import { ChatMessage, BotFormFields } from '../types/types';
import { RendererType } from '../types/enums';
import MessageRenderer from '../Renderers/MessageRenderer';
import FormRenderer from '../Renderers/FormRenderer';
import FeedbackRenderer from '../Renderers/FeedbackRenderer';

export interface CommonRendererProps {
  msg: ChatMessage;
  idx: number;
  lastFormPrompt?: ChatMessage | null;
  formLocked?: boolean;
  onFormSubmit?: (fields: BotFormFields) => Promise<void>;
  lastFormFieldValues?: BotFormFields;
}

export type RendererComponent = React.ComponentType<CommonRendererProps>;

/**
 * Registry of renderer components for different message types
 */
export const rendererComponents: Record<RendererType, RendererComponent> = {
  [RendererType.USER_MESSAGE]: MessageRenderer,
  [RendererType.MESSAGE]: MessageRenderer,
  [RendererType.FORM]: Form<PERSON><PERSON>er,
  [RendererType.FEEDBACK]: FeedbackRenderer,
};

/**
 * Gets the appropriate renderer for a message type
 */
export const getRenderer = (nodeType: RendererType): RendererComponent | null => {
  return rendererComponents[nodeType] || null;
};

/**
 * Registers a new renderer component
 */
export const registerRenderer = (nodeType: RendererType, component: RendererComponent): void => {
  rendererComponents[nodeType] = component;
};
