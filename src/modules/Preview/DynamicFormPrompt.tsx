'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Form, FormField, FormItem, FormControl, FormMessage } from '@/components/ui/form';
import { ControllerRenderProps, useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/DatePicker/date-picker';
import TimePicker from '@/components/time-picker';
import { FormFieldData, BotFormFields, ChatMessage, FormData } from './types/types';
import { DateStartRange, DateEndRange } from './types/constants';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { TimeFormat, FormFieldType } from './types/enums';
import { zodResolver } from '@hookform/resolvers/zod';
import { createFormValidationSchema } from './schema'; // adjust path

interface DynamicFormPromptProps {
  message: ChatMessage;
  formDisabled: boolean;
  onSubmit: (fields: BotFormFields) => Promise<void>;
  defaultValues?: BotFormFields;
}

const DynamicFormPrompt: React.FC<DynamicFormPromptProps> = ({
  message,
  formDisabled,
  onSubmit,
  defaultValues,
}) => {
  const { t } = useTranslation();
  const formData = message.data as FormData;
  const mergedDefaults = { ...defaultValues, ...formData.submittedValues };

  const promptFields: FormFieldData[] = formData.prompt ?? [];
  const validationSchema = createFormValidationSchema(promptFields);
  const form = useForm<BotFormFields>({
    resolver: zodResolver(validationSchema),
    mode: 'onTouched', // ✅ only validate after user leaves the field
    reValidateMode: 'onChange', // ✅ revalidate when user edits
    defaultValues: mergedDefaults,
  });

  const { control, handleSubmit, formState } = form;

  const renderFieldComponent = (
    field: FormFieldData,
    rhfField: ControllerRenderProps<BotFormFields, string>
  ) => {
    const fieldType = field.fieldType || FormFieldType.TEXT;
    const commonProps = {
      disabled: formDisabled,
      className: cn(
        'w-full border-tertiary-200 text-tertiary-500',
        formDisabled ? 'opacity-50 pointer-events-none' : ''
      ),
    };

    switch (fieldType) {
      case FormFieldType.TIME:
        return (
          <TimePicker
            {...commonProps}
            value={typeof rhfField.value === 'string' ? rhfField.value : ''}
            onChange={rhfField.onChange}
            format={TimeFormat.TWELVE_HOUR}
          />
        );

      case FormFieldType.DATE:
      case FormFieldType.PAST_DATE:
      case FormFieldType.FUTURE_DATE:
      case FormFieldType.CUSTOM_DATE:
        return (
          <DatePicker
            {...commonProps}
            selected={rhfField.value as any}
            onSelect={d => rhfField.onChange(d ?? '')}
            fieldType={fieldType as any}
            customRange={
              field.fieldType === FormFieldType.CUSTOM_DATE
                ? {
                    from: new Date(field.rangeStart || DateStartRange),
                    to: new Date(field.rangeEnd || DateEndRange),
                  }
                : undefined
            }
            className={cn(
              'w-full max-w-full truncate border border-tertiary-200 text-tertiary-500 overflow-hidden',
              formDisabled ? 'opacity-50 pointer-events-none' : ''
            )}
          />
        );

      case FormFieldType.MOBILE_NUMBER:
        return (
          <div className="w-full !border h-10 !rounded-lg">
            <PhoneInput
              defaultCountry="in"
              disabled={formDisabled}
              value={String(rhfField.value || '')}
              onChange={val => rhfField.onChange(val)}
              onBlur={rhfField.onBlur}
              className="w-full !h-full !p-0.5 !rounded-lg"
              inputClassName="w-full !h-full !border-0"
              countrySelectorStyleProps={{ buttonClassName: '!border-0' }}
            />
          </div>
        );

      default:
        return (
          <Input
            type={fieldType}
            placeholder={field.fieldName}
            value={String(rhfField.value || '')}
            onChange={rhfField.onChange}
            onBlur={rhfField.onBlur}
            name={rhfField.name}
            disabled={formDisabled}
            className="w-full bg-background border border-tertiary-200 rounded-lg px-3 py-2.5 text-base placeholder-tertiary-400 focus:outline-none focus:ring-2 focus:ring-primary-300"
          />
        );
    }
  };

  return (
    <Form {...form}>
      <form
        className="space-y-3 p-2 mb-3 border border-muted-400 rounded-lg"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        {promptFields.map(field => (
          <div key={field.fieldName} className="relative">
            <label className="mb-1 font-medium text-tertiary-700 block">
              {field.label ?? field.fieldName}
              {field.required ? <span className="ml-1 text-error-600">*</span> : null}
            </label>
            <FormField
              control={control}
              name={field.fieldName}
              rules={{ required: field.required }}
              render={({ field: rhfField }) => (
                <FormItem>
                  <FormControl>{renderFieldComponent(field, rhfField)}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        ))}

        <Button type="submit" className="mt-2 w-full" disabled={formDisabled || !formState.isValid}>
          {t('common.submit')}
        </Button>
      </form>
    </Form>
  );
};

export default DynamicFormPrompt;
