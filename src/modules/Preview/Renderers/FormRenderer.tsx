import DynamicFormPrompt from '../DynamicFormPrompt';
import { RendererType } from '../types/enums';
import { CommonRendererProps } from '../utils/rendererRegistry';

const FormRenderer: React.FC<CommonRendererProps> = ({
  msg,
  idx,
  lastFormPrompt,
  formLocked = false,
  onFormSubmit = () => Promise.resolve(),
  lastFormFieldValues,
}) => {
  if (msg.nodeType === RendererType.FORM && msg.data?.prompt?.length) {
    const isLocked = !!msg.data?.submittedValues || (lastFormPrompt !== msg && formLocked);
    const promptFields = msg.data.prompt;
    const singleField = promptFields.length === 1 ? promptFields[0] : null;

    if (singleField && singleField.fieldType === 'text') {
      return null;
    }

    return (
      <div key={idx} className="flex justify-start">
        <div className="bg-transparent text-secondary-900 w-full max-w-[90%]">
          <DynamicFormPrompt
            message={msg}
            formDisabled={isLocked}
            onSubmit={onFormSubmit}
            defaultValues={msg.data?.submittedValues ?? lastFormFieldValues}
          />
        </div>
      </div>
    );
  }

  return null;
};

export default FormRenderer;
