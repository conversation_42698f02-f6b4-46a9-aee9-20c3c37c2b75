import { useForm } from 'react-hook-form';
import { Star, ThumbsUp, ThumbsDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Form, FormField, FormItem, FormMessage, FormControl } from '@/components/ui/form';
import { BotFormFields, FeedbackData } from '../types/types';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { CommonRendererProps } from '../utils/rendererRegistry';

const FeedbackRenderer: React.FC<CommonRendererProps> = ({
  msg,
  idx,
  onFormSubmit = () => Promise.resolve(),
  formLocked = false,
  lastFormPrompt,
}) => {
  const feedbackData = msg.data as FeedbackData;
  const feedbackType = feedbackData.type || 'Star'; // Default fallback
  const feedbackPrompt = feedbackData.text || 'Please provide your feedback:';
  const { t } = useTranslation();
  const form = useForm<{ feedback: string }>({
    defaultValues: { feedback: '' },
  });
  const isLocked = !!feedbackData.submittedValues || (lastFormPrompt !== msg && formLocked);
  const handleSubmit = form.handleSubmit(async values => {
    const data: BotFormFields = {
      feedback: values.feedback,
      feedbackType,
    };
    await onFormSubmit(data);
  });

  return (
    <Form {...form}>
      <form
        key={idx}
        onSubmit={handleSubmit}
        className="p-4 rounded-xl bg-card w-full max-w-md shadow space-y-4"
      >
        <p className="text-sm font-medium">{feedbackPrompt}</p>

        <FormField
          control={form.control}
          name="feedback"
          rules={{ required: 'Feedback is required' }}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div>
                  {/* --- Star Rating --- */}
                  {feedbackType === 'Star' && (
                    <div className="flex gap-2">
                      {Array.from({ length: 5 }, (_, i) => {
                        const starValue = (i + 1).toString();
                        const isActive = field.value && starValue <= field.value;

                        return (
                          <Star
                            key={starValue}
                            className={cn(
                              'w-7 h-7 transition-colors',
                              isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                              isActive ? 'text-warning-500' : 'text-muted-foreground'
                            )}
                            onClick={!isLocked ? () => field.onChange(starValue) : undefined}
                            fill={isActive ? 'currentColor' : 'none'}
                          />
                        );
                      })}
                    </div>
                  )}

                  {/* --- Thumbs --- */}
                  {feedbackType === 'Thumbs' && (
                    <div className="flex gap-1">
                      <ThumbsUp
                        className={cn(
                          'w-7 h-7 transition-colors',
                          isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                          field.value === '1' ? 'text-success-500' : 'text-muted-foreground'
                        )}
                        onClick={!isLocked ? () => field.onChange('1') : undefined}
                        fill={field.value === '1' ? 'currentColor' : 'none'}
                      />
                      <ThumbsDown
                        className={cn(
                          'w-7 h-7 transition-colors',
                          isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                          field.value === '0' ? 'text-error-500' : 'text-muted-foreground'
                        )}
                        onClick={!isLocked ? () => field.onChange('0') : undefined}
                        fill={field.value === '0' ? 'currentColor' : 'none'}
                      />
                    </div>
                  )}

                  {/* --- Text Rating --- */}
                  {feedbackType === 'Text' && (
                    <div className="flex gap-2">
                      {Array.from({ length: 5 }, (_, i) => {
                        const num = (i + 1).toString();
                        const isSelected = field.value === num;
                        return (
                          <button
                            key={num}
                            type="button"
                            onClick={() => field.onChange(num)}
                            className={cn(
                              'px-3 py-1 border rounded-lg text-sm font-medium transition-colors',
                              isSelected
                                ? 'bg-primary text-primary-foreground border-primary'
                                : 'bg-background text-muted-foreground border-input',
                              isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                            )}
                            disabled={isLocked}
                          >
                            {num}
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={!form.watch('feedback') || isLocked}
          className="w-full mt-4"
        >
          {t('common.submit')}
        </Button>
      </form>
    </Form>
  );
};

export default FeedbackRenderer;
