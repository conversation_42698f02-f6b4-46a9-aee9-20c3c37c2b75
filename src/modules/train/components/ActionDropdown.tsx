import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVertical, SquarePen, Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ActionDropdownProps {
  onEdit: () => void;
  onDelete: () => void;
  disableDelete?: boolean;
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({ onEdit, onDelete, disableDelete }) => {
  const { t } = useTranslation();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <MoreVertical className="text-tertiary-600 w-5 h-5 cursor-pointer" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={onEdit} className="flex gap-2">
          <SquarePen />
          {t('common.edit')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={onDelete}
          disabled={disableDelete}
          className="flex gap-2 text-error-500"
        >
          <Trash2 />
          {t('common.delete')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ActionDropdown;
