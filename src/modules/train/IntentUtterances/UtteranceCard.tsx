import { IntentUtteranceTranslation } from '@/types';
import React from 'react';
import ActionDropdown from '../components/ActionDropdown';
import TagBox from '@/components/TagBox';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useTranslation } from 'react-i18next';

interface IProps {
  utteranceTranslation: IntentUtteranceTranslation;
  onEdit: (utterance: IntentUtteranceTranslation) => void;
  onDelete: (utterance: IntentUtteranceTranslation) => void;
}

// Function to render text with entity highlights
const renderTextWithEntities = (text: string, t: (key: string) => string) => {
  const entityRegex = /\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;

  while ((match = entityRegex.exec(text)) !== null) {
    // Add text before entity
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }

    // Add highlighted entity with tooltip
    parts.push(
      <TooltipProvider key={match[3]}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="bg-primary-100 text-primary-800 px-1 py-0.5 rounded text-sm font-medium cursor-help">
              {match[1]}
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              <div className="font-medium">{t('entities.table.name')}: {match[2]}</div>
              <div className="text-tertiary-50-500">{t('entities.table.type')}: {match[4]}</div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return parts.length > 0 ? parts : text;
};

const UtteranceCard: React.FC<IProps> = ({ utteranceTranslation, onEdit, onDelete }) => {
  const { t } = useTranslation();
  return (
    <div
      className="bg-background rounded-lg shadow-sm border border-tertiary-200 p-4 mb-4 flex justify-between items-start"
      data-testid="question-card"
    >
      <div className="text-tertiary-700 flex-1 mr-4">
        {renderTextWithEntities(utteranceTranslation.text, t)}
      </div>

      <div className="flex gap-3">
        <div className="flex gap-2">
          {utteranceTranslation.availableLanguages?.map(lang => (
            <TagBox key={lang.id} text={lang.name} />
          ))}
        </div>
        <div className="flex items-center space-x-4">
          <ActionDropdown
            onEdit={() => onEdit(utteranceTranslation)}
            onDelete={() => onDelete(utteranceTranslation)}
          />
        </div>
      </div>
    </div>
  );
};

export default UtteranceCard;
