import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FloatingField } from '@/components/ui/floating-label';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useTranslation } from 'react-i18next';
import { IntentItem } from '@/types';
import { useUpdateIntentItemMutation, useCreateIntentItemMutation } from '@/store/api';
import RenderButtons from '@/modules/train/components/RenderButtons';
import { AddIntentFormValues, createAddIntentFormSchema } from '../schema';

interface AddIntentFormProps {
  botId: string;
  onClose: (intent?: IntentItem) => void;
  intent?: IntentItem;
}

const AddIntentForm: React.FC<AddIntentFormProps> = ({ botId, onClose, intent }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [updateIntentItem] = useUpdateIntentItemMutation();
  const [createIntentItem] = useCreateIntentItemMutation();

  const form = useForm<AddIntentFormValues>({
    resolver: zodResolver(createAddIntentFormSchema(t)),
    defaultValues: {
      intentName: intent?.name || '',
    },
  });

  const onSubmit = async (values: AddIntentFormValues) => {
    try {
      let resultIntent: IntentItem | undefined;
      if (intent) {
        const result = await updateIntentItem({ id: intent.id, name: values.intentName }).unwrap();
        resultIntent = result.data;
        toast({
          title: <SuccessToastMessage message={t('intents.intentUpdated')} />,
        });
      } else {
        const result = await createIntentItem({ botId, name: values.intentName }).unwrap();
        resultIntent = result.data;
        toast({
          title: <SuccessToastMessage message={t('intents.intentAdded')} />,
        });
      }
      onClose(resultIntent);
    } catch (error: any) {
      console.error('Failed to save intent:', error);
      toast({
        title: t('common.error'),
        description: error?.data?.error?.message || t('common.somethingWrong'),
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="intentName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  id="addInput"
                  label={t('intents.nameLabel')}
                  aria-label="Intent name"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <RenderButtons
          handleClose={() => onClose()}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!intent}
        />
      </form>
    </Form>
  );
};

export default AddIntentForm;
