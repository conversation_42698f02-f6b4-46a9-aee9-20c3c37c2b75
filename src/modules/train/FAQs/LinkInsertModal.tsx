import React, { useState, useMemo, useRef, useEffect } from 'react';
import { displayTextSchema, useURLValidator } from '@/hooks/useValidator';
import { FloatingField } from '@/components/ui/floating-label';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';
import { MESSAGES } from '@/constants/message.constants';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface LinkInsertionPopoverProps {
  onInsert: (url: string, displayText: string) => void;
  onUpdate: (url: string, displayText: string) => void;
  onRemove: () => void;
  onClose: () => void;
  isOpen: boolean;
  toggleButtonRef?: React.RefObject<HTMLElement>;
  initialUrl?: string;
  initialDisplayText?: string;
}

export const InboxLinkInsertModal: React.FC<LinkInsertionPopoverProps> = ({
  onInsert,
  onUpdate,
  onRemove,
  onClose,
  isOpen,
  toggleButtonRef,
  initialUrl = '',
  initialDisplayText = '',
}) => {
  const [displayText, setDisplayText] = useState(initialDisplayText);
  const [url, setUrl] = useState(initialUrl);
  const modalRef = useRef<HTMLDivElement>(null);
  const { validateURL } = useURLValidator();
  const [displayTextError, setDisplayTextError] = useState<string | null>(null);
  const [urlError, setUrlError] = useState<string | null>(null);
  const { t } = useTranslation();

  const urlSchema = useMemo(
    () =>
      z
        .string()
        .min(1, 'URL is required')
        .superRefine((val, ctx) => {
          const [isValid, errorMessage] = validateURL(val);
          if (!isValid) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: errorMessage,
            });
          }
        }),
    [validateURL]
  );

  const isDisabled = !url.trim();

  const handleUpdate = () => {
    if (isDisabled) return;

    setDisplayTextError(null);
    setUrlError(null);

    const trimmedUrl = url.trim();
    const trimmedDisplayText = displayText.trim();

    let hasError = false;

    const displayTextValidationResult = displayTextSchema.safeParse(trimmedDisplayText);
    if (!displayTextValidationResult.success) {
      setDisplayTextError(displayTextValidationResult.error.errors[0].message);
      hasError = true;
    }

    const urlValidationResult = urlSchema.safeParse(trimmedUrl);
    if (!urlValidationResult.success) {
      setUrlError(urlValidationResult.error.errors[0].message);
      hasError = true;
    }

    if (hasError) return;

    onUpdate(trimmedUrl, trimmedDisplayText || trimmedUrl);
    setDisplayText('');
    setUrl('');
    onClose();
  };

  const handleInsert = () => {
    if (isDisabled) return;

    setDisplayTextError(null);
    setUrlError(null);

    const trimmedUrl = url.trim();
    const trimmedDisplayText = displayText.trim();

    let hasError = false;

    const displayTextValidationResult = displayTextSchema.safeParse(trimmedDisplayText);
    if (!displayTextValidationResult.success) {
      setDisplayTextError(displayTextValidationResult.error.errors[0].message);
      hasError = true;
    }

    const urlValidationResult = urlSchema.safeParse(trimmedUrl);
    if (!urlValidationResult.success) {
      setUrlError(urlValidationResult.error.errors[0].message);
      hasError = true;
    }

    if (hasError) return;

    onInsert(trimmedUrl, trimmedDisplayText || trimmedUrl);
    setDisplayText('');
    setUrl('');
    onClose();
  };

  const handleRemove = () => {
    onRemove();
    setDisplayText('');
    setUrl('');
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setDisplayText(initialDisplayText);
      setUrl(initialUrl);
    }
  }, [isOpen, initialDisplayText, initialUrl]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isClickOutsideModal =
        modalRef.current && !modalRef.current.contains(event.target as Node);
      const isClickOutsideToggle =
        toggleButtonRef?.current && !toggleButtonRef.current.contains(event.target as Node);
      if (isClickOutsideModal && isClickOutsideToggle) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      setDisplayTextError(null);
      setUrlError(null);
      setDisplayText('');
      setUrl('');
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, toggleButtonRef]);

  if (!isOpen) return null;

  return (
    <div
      ref={modalRef}
      className="absolute bottom-9 right-0 w-96 bg-white p-5 rounded-lg shadow-xl border border-tertiary-200 z-50"
    >
      <div className="flex flex-col gap-4">
        <FloatingField
          label={t(MESSAGES.INSERT_LINK_MODAL_TEXT_LABEL)}
          value={displayText}
          onChange={setDisplayText}
          type="text"
          className={cn(
            'w-full pl-2.5 h-11 rounded-sm text-xs',
            displayTextError ? 'border border-error-500' : 'border border-tertiary-300'
          )}
          disabled={false}
          data-testid="display-text"
        />
        <FloatingField
          label={t(MESSAGES.INSERT_LINK_MODAL_LINK_LABEL)}
          value={url}
          onChange={setUrl}
          type="text"
          className={cn(
            'w-full pl-2.5 h-11 rounded-sm text-xs',
            urlError ? 'border border-error-500' : 'border border-tertiary-300'
          )}
          disabled={false}
          data-testid="url-input"
        />
        <div className="flex mt-5 justify-end gap-2">
          <Button
            variant="ghost"
            type="button"
            className="px-6 py-1.5 text-sm font-medium text-tertiary-600 rounded-sm border border-tertiary-300"
            onClick={onClose}
          >
            {t(MESSAGES.CANCEL).toUpperCase()}
          </Button>
          <Button
            type="button"
            className={cn(
              'px-6 py-1.5 text-sm font-medium text-white rounded-sm',
              isDisabled
                ? 'bg-tertiary-300 cursor-not-allowed'
                : 'bg-primary-500 hover:bg-primary-600'
            )}
            onClick={initialUrl ? handleUpdate : handleInsert}
            disabled={isDisabled}
          >
            {initialUrl ? t(MESSAGES.UPDATE).toUpperCase() : t(MESSAGES.INSERT).toUpperCase()}
          </Button>
          {initialUrl && (
            <Button
              type="button"
              variantColor="error"
              className="px-6 py-1.5 text-sm font-medium rounded-sm"
              onClick={handleRemove}
            >
              {t(MESSAGES.REMOVE)}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
