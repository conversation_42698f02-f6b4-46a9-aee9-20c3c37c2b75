import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import LeftPanel from '@/modules/train/components/LeftPanel';
import RightPanel from '@/modules/train/components/RightPanel';
import { useDeleteFaqCategoryMutation, useGetFaqCategoriesQuery } from '@/store/api';
import { FaqCategory, FaqCategoryType, ModalStateCUD, OrderDirection } from '@/types';
import RightPanelContent from './RightPanelContent';
import AddModal from '../components/AddModal';
import AddCategoryForm from './AddCategoryForm';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import ActionDropdown from '../components/ActionDropdown';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useBotIdParam } from '@/hooks/useRouterParam';
import {
  PaginationProvider,
  usePagination,
  PaginationRenderItems,
  PaginationLoader,
  PaginationEmptyState,
  PaginationError,
} from '@/components/Pagination';
import PaginatedListItems from '@/modules/train/components/PaginatedListItems';
import EmptyState from '@/components/EmptyState';

const FAQsTab: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { botId } = useBotIdParam();

  const pagination = usePagination({
    queryHook: query =>
      useGetFaqCategoriesQuery(
        {
          filter: {
            botId: {
              eq: botId,
            },
          },
          order: [
            ['type', OrderDirection.DESC],
            ['createdAt', OrderDirection.DESC],
          ],
          ...query,
        },
        { skip: !botId }
      ),
  });

  const [deleteFaqCategory] = useDeleteFaqCategoryMutation();

  const [selectedCategory, setSelectedCategory] = useState<FaqCategory | null>(null);
  const [modalState, setModalState] = useState<ModalStateCUD<FaqCategory> | null>(null);

  const {
    queryState: { data: categoriesData, isFetching },
  } = pagination;
  const items = categoriesData?.data?.items || [];

  useEffect(() => {
    if (items.length && !selectedCategory && !isFetching) {
      setSelectedCategory(items[0]);
    }
  }, [items, selectedCategory]);

  const handleCreateToggle = () => {
    setModalState(prev => (prev?.type === 'create' ? null : { type: 'create' }));
  };

  const handleSelectCategory = (category: FaqCategory) => {
    setSelectedCategory(category);
  };

  const handleEditCategory = useCallback((category: FaqCategory) => {
    setModalState({ type: 'edit', item: category });
  }, []);

  const handleDeleteCategory = useCallback((category: FaqCategory) => {
    setModalState({ type: 'delete', item: category });
  }, []);

  const handleCloseModal = () => {
    setModalState(null);
  };

  const confirmDeleteCategory = async () => {
    if (!modalState || modalState.type !== 'delete') return;
    try {
      await deleteFaqCategory({ id: modalState.item.id }).unwrap();
      setSelectedCategory(null);
      toast({ title: <SuccessToastMessage message={t('faqs.category.categoryDeleted')} /> });
    } catch (error: any) {
      console.error('Failed to delete FAQ category:', error);
      toast({
        title: t('common.error'),
        description: error?.data?.error?.message || t('common.somethingWrong'),
        variant: 'destructive',
      });
    } finally {
      handleCloseModal();
    }
  };

  const renderCategoryItem = useCallback(
    (category: FaqCategory) => {
      const isDefault = category.type === FaqCategoryType.DEFAULT;

      return (
        <>
          <span className="text-sm font-medium text-tertiary-700 truncate">{category.name}</span>
          <ActionDropdown
            onEdit={() => handleEditCategory(category)}
            onDelete={() => handleDeleteCategory(category)}
            disableDelete={isDefault}
          />
        </>
      );
    },
    [handleEditCategory, handleDeleteCategory]
  );

  const CategoryModel = modalState && modalState?.type !== 'delete' && (
    <AddModal
      title={modalState.item?.id ? t('faqs.category.editTitle') : t('faqs.category.addTitle')}
      open={true}
      onOpenChange={handleCloseModal}
    >
      <AddCategoryForm botId={botId} onClose={handleCloseModal} category={modalState.item} />
    </AddModal>
  );

  return (
    <div className="flex h-full">
      <PaginationProvider value={pagination}>
        <LeftPanel
          title={t('faqs.category.title')}
          addTrigger={
            <Button
              size="icon"
              className="w-fit h-fit rounded-sm"
              onClick={handleCreateToggle}
              data-testid="add-category-button"
              aria-label="Add category"
            >
              <Plus className="!h-6 !w-6" />
            </Button>
          }
        >
          <PaginationRenderItems<FaqCategory>
            renderItems={items => (
              <PaginatedListItems
                items={items}
                selectedItemId={selectedCategory?.id}
                onSelectItem={handleSelectCategory}
                renderItemContent={renderCategoryItem}
              />
            )}
          />
          <PaginationLoader />
          <PaginationEmptyState>
            <div className="flex flex-col h-full items-center justify-center">
              <EmptyState
                className="w-full"
                title={t('faqs.category.startAdding')}
                description={t('common.nothingToShow')}
              >
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={handleCreateToggle}
                  data-testid="add-category-button-empty"
                >
                  {t('faqs.category.addTitle')}
                </Button>
              </EmptyState>
            </div>
          </PaginationEmptyState>
          <PaginationError />
        </LeftPanel>
      </PaginationProvider>

      <RightPanel title={t('faqs.title')}>
        {selectedCategory ? (
          <RightPanelContent selectedCategory={selectedCategory} />
        ) : (
          <div className="flex-1 flex items-center justify-center text-tertiary-500">
            <p>{t('faqs.category.selectToManage')}</p>
          </div>
        )}
      </RightPanel>
      {CategoryModel}

      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDeleteCategory}
        title={t('faqs.category.confirmDeleteTitle')}
        description={t('faqs.category.deleteConfirmationMessage')}
      />
    </div>
  );
};

export default FAQsTab;
