import { render, screen } from '@/test/utils';
import SmallTalkTab from '../SmallTalk';
import { describe, expect, it, vi } from 'vitest';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('SmallTalkTab', () => {
  it('renders SmallTalk component with translated content', () => {
    render(<SmallTalkTab />);

    expect(screen.getByText('train.smallTalk.content')).toBeInTheDocument();
  });

  it('uses translation hook correctly', () => {
    render(<SmallTalkTab />);

    const container = screen.getByText('train.smallTalk.content');
    expect(container.tagName).toBe('DIV');
  });
});
