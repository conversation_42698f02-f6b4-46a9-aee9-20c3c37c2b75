import { render, screen } from '@/test/utils';
import TrainFromLogsTab from '../TrainFromLogs';
import { describe, expect, it, vi } from 'vitest';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('TrainFromLogsTab', () => {
  it('renders TrainFromLogs component with translated content', () => {
    render(<TrainFromLogsTab />);

    expect(screen.getByText('train.trainFromLogs.content')).toBeInTheDocument();
  });

  it('uses translation hook correctly', () => {
    render(<TrainFromLogsTab />);

    const container = screen.getByText('train.trainFromLogs.content');
    expect(container.tagName).toBe('DIV');
  });
});
