import { render, screen } from '@/test/utils';
import SynonymsTab from '../Synonyms';
import { describe, expect, it, vi } from 'vitest';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('SynonymsTab', () => {
  it('renders Synonyms component with translated content', () => {
    render(<SynonymsTab />);

    expect(screen.getByText('train.synonyms.content')).toBeInTheDocument();
  });

  it('uses translation hook correctly', () => {
    render(<SynonymsTab />);

    const container = screen.getByText('train.synonyms.content');
    expect(container.tagName).toBe('DIV');
  });
});
