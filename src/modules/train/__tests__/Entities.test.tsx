import { render, screen, waitFor, createPaginatedResponse } from '@/test/utils';
import userEvent from '@testing-library/user-event';
import * as storeApi from '@/store/api';
import EntitiesTab from '../Entities';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EntityType } from '@/types';
import React from 'react';

// Mock the API
vi.mock('@/store/api', async () => {
  const actual = await vi.importActual('@/store/api');
  return {
    ...actual,
    useGetEntitiesQuery: vi.fn(),
    useCreateEntityMutation: vi.fn(),
    useUpdateEntityMutation: vi.fn(),
    useDeleteEntityMutation: vi.fn(),
  };
});

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [new URLSearchParams('?id=bot1'), vi.fn()],
  };
});

const mockEntities = [
  {
    id: '1',
    name: 'PersonName',
    type: EntityType.TEXT,
    botId: 'bot1',
    intentId: null,
    metadata: {},
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
  },
  {
    id: '2',
    name: 'EmailAddress',
    type: EntityType.EMAIL,
    botId: 'bot1',
    intentId: 'intent1',
    metadata: {},
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
  },
  {
    id: '3',
    name: 'PhonePattern',
    type: EntityType.REGEX,
    botId: 'bot1',
    intentId: null,
    metadata: { value: '^\\+?[1-9]\\d{1,14}$' },
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
  },
];

describe('EntitiesTab', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default successful API responses with proper pagination structure
    const paginatedResponse = createPaginatedResponse(mockEntities, {
      page: 1,
      limit: 10,
      total: 3,
    });
    vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
      data: paginatedResponse.data,
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
      isUninitialized: false,
      isFetching: false,
      isSuccess: true,
      status: 'fulfilled' as any,
    } as any);

    const mockCreateEntity = vi.fn().mockResolvedValue({
      data: {
        id: 'new-entity',
        name: 'New Entity',
        type: EntityType.TEXT,
        botId: 'bot1',
        intentId: null,
        metadata: {},
      },
    });
    vi.mocked(storeApi.useCreateEntityMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({ unwrap: mockCreateEntity })),
      { isLoading: false, isError: false },
    ] as any);

    const mockUpdateEntity = vi.fn().mockResolvedValue({
      data: {
        id: '1',
        name: 'Updated Entity',
        type: EntityType.TEXT,
        botId: 'bot1',
        intentId: null,
        metadata: {},
      },
    });
    vi.mocked(storeApi.useUpdateEntityMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({ unwrap: mockUpdateEntity })),
      { isLoading: false, isError: false },
    ] as any);

    const mockDeleteEntity = vi.fn().mockResolvedValue({ data: { success: true } });
    vi.mocked(storeApi.useDeleteEntityMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({ unwrap: mockDeleteEntity })),
      { isLoading: false, isError: false },
    ] as any);
  });

  describe('Rendering and Basic Functionality', () => {
    it('renders entities tab with title and search functionality', () => {
      render(<EntitiesTab />);

      expect(screen.getByText('entities.title')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('common.search')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Filter entities' })).toBeInTheDocument();
      expect(screen.getByText('entities.addTitle')).toBeInTheDocument();
    });

    it('displays entities in table format', () => {
      render(<EntitiesTab />);

      expect(screen.getByText('PersonName')).toBeInTheDocument();
      expect(screen.getByText('EmailAddress')).toBeInTheDocument();
      expect(screen.getByText('PhonePattern')).toBeInTheDocument();
      expect(screen.getByText('TEXT')).toBeInTheDocument();
      expect(screen.getByText('EMAIL')).toBeInTheDocument();
      expect(screen.getByText('REGEX')).toBeInTheDocument();
    });

    it('displays metadata values correctly', () => {
      render(<EntitiesTab />);

      // REGEX entity should show its pattern
      expect(screen.getByText('^\\+?[1-9]\\d{1,14}$')).toBeInTheDocument();

      // Non-REGEX entities should show dash
      const dashElements = screen.getAllByText('-');
      expect(dashElements).toHaveLength(2); // PersonName and EmailAddress
    });

    it('shows table headers correctly', () => {
      render(<EntitiesTab />);

      expect(screen.getByText('entities.table.name')).toBeInTheDocument();
      expect(screen.getByText('entities.table.type')).toBeInTheDocument();
      expect(screen.getByText('entities.table.value')).toBeInTheDocument();
      expect(screen.getByText('entities.table.action')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('filters entities by name', async () => {
      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('common.search');
      await userEvent.type(searchInput, 'Person');

      // Note: Search functionality is handled by the pagination hook,
      // so we just verify the search input works
      expect(searchInput).toHaveValue('Person');
    });

    it('filters entities case-insensitively', async () => {
      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('common.search');
      await userEvent.type(searchInput, 'email');

      expect(searchInput).toHaveValue('email');
    });

    it('shows all entities when search is cleared', async () => {
      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('common.search');
      await userEvent.type(searchInput, 'Person');
      await userEvent.clear(searchInput);

      expect(searchInput).toHaveValue('');
    });

    it('shows no results when search matches nothing', async () => {
      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('common.search');
      await userEvent.type(searchInput, 'NonExistentEntity');

      expect(searchInput).toHaveValue('NonExistentEntity');
    });
  });

  describe('Loading and Error States', () => {
    it('displays loading state', () => {
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: undefined,
        isLoading: true,
        isError: false,
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      expect(screen.getByText('common.loading')).toBeInTheDocument();
    });

    it('displays error state', () => {
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: undefined,
        isLoading: false,
        isError: true,
        error: { message: 'Failed to load entities' },
        refetch: vi.fn(),
        isUninitialized: false,
        isFetching: false,
        isSuccess: false,
        status: 'rejected' as any,
      } as any);

      render(<EntitiesTab />);

      expect(screen.getByText('pagination.errorLoadingData')).toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('displays empty state when no entities exist', () => {
      const emptyPaginatedResponse = createPaginatedResponse([], { page: 1, limit: 10, total: 0 });
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: emptyPaginatedResponse.data,
        isLoading: false,
        isError: false,
        refetch: vi.fn(),
        isUninitialized: false,
        isFetching: false,
        isSuccess: true,
        status: 'fulfilled' as any,
      } as any);

      render(<EntitiesTab />);

      expect(screen.getByText('entities.startAdding')).toBeInTheDocument();
      expect(screen.getByText('common.nothingToShow')).toBeInTheDocument();
      expect(screen.getAllByText('entities.addTitle')).toHaveLength(2); // One in header, one in empty state
    });
  });

  describe('Entity Creation', () => {
    it('opens add entity modal when add button is clicked', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]); // Click the first one (header button)

      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('creates a new TEXT entity successfully', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]); // Click the first one (header button)

      const nameInput = screen.getByLabelText('Entity name');
      await userEvent.type(nameInput, 'TestEntity');

      // The default type is TEXT, so we can submit directly
      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('shows form with entity name input and type selector', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]);

      // Check that form elements are present
      expect(screen.getByLabelText('Entity name')).toBeInTheDocument();
      expect(screen.getByLabelText('Select type')).toBeInTheDocument();
      expect(screen.getByLabelText('Form Submit Button')).toBeInTheDocument();
      expect(screen.getByText('common.cancel')).toBeInTheDocument();
    });

    it('validates required fields in entity form', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]);

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      // Form should still be open due to validation errors
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('validates required entity name field', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]);

      // Try to submit without filling name
      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      // Form should still be open due to validation error
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('closes modal when cancel button is clicked', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]);

      const cancelButton = screen.getByText('common.cancel');
      await userEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('handles entity creation failure gracefully', async () => {
      const mockCreateEntity = vi.fn().mockRejectedValue(new Error('Creation failed'));
      vi.mocked(storeApi.useCreateEntityMutation).mockReturnValue([
        vi.fn().mockImplementation(() => ({ unwrap: mockCreateEntity })),
        { isLoading: false, isError: true },
      ] as any);

      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      const nameInput = screen.getByLabelText(/entity name/i);
      await userEvent.type(nameInput, 'TestEntity');

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(mockCreateEntity).toHaveBeenCalled();
      });
    });
  });

  describe('Entity Editing', () => {
    it('opens edit modal when edit button is clicked', async () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByLabelText('Edit');
      await userEvent.click(editButtons[0]);

      expect(screen.getByText('entities.editTitle')).toBeInTheDocument();
      expect(screen.getByDisplayValue('PersonName')).toBeInTheDocument();
    });

    it('updates entity successfully', async () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByLabelText('Edit');
      await userEvent.click(editButtons[0]);

      const nameInput = screen.getByDisplayValue('PersonName');
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, 'UpdatedPersonName');

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByText('entities.editTitle')).not.toBeInTheDocument();
      });
    });

    it('pre-fills form with existing entity data', async () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByLabelText('Edit');
      await userEvent.click(editButtons[2]); // PhonePattern entity

      expect(screen.getByDisplayValue('PhonePattern')).toBeInTheDocument();
      expect(screen.getByDisplayValue('^\\+?[1-9]\\d{1,14}$')).toBeInTheDocument();
    });

    it('handles entity update failure gracefully', async () => {
      const mockUpdateEntity = vi
        .fn()
        .mockRejectedValue({ data: { error: { message: 'Update failed' } } });
      vi.mocked(storeApi.useUpdateEntityMutation).mockReturnValue([
        vi.fn().mockImplementation(() => ({ unwrap: mockUpdateEntity })),
        { isLoading: false, isError: true },
      ] as any);

      render(<EntitiesTab />);

      const editButtons = screen.getAllByLabelText('Edit');
      await userEvent.click(editButtons[0]);

      const nameInput = screen.getByDisplayValue('PersonName');
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, 'UpdatedPersonName');

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(mockUpdateEntity).toHaveBeenCalled();
        // Form should still be open due to error
        expect(screen.getByText('entities.editTitle')).toBeInTheDocument();
      });
    });
  });

  describe('Entity Deletion', () => {
    it('opens delete confirmation modal when delete button is clicked', async () => {
      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByLabelText('Delete');
      await userEvent.click(deleteButtons[0]);

      expect(screen.getByText('entities.confirmDeleteTitle')).toBeInTheDocument();
      expect(screen.getByText('entities.deleteConfirmationMessage')).toBeInTheDocument();
    });

    it('deletes entity when confirmed', async () => {
      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByLabelText('Delete');
      await userEvent.click(deleteButtons[0]);

      const confirmButton = screen.getByText('chatbot.yesDelete');
      await userEvent.click(confirmButton);

      await waitFor(() => {
        expect(screen.queryByText('entities.confirmDeleteTitle')).not.toBeInTheDocument();
      });
    });

    it('cancels deletion when cancel button is clicked', async () => {
      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByLabelText('Delete');
      await userEvent.click(deleteButtons[0]);

      const cancelButton = screen.getByText('chatbot.noCancel');
      await userEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByText('entities.confirmDeleteTitle')).not.toBeInTheDocument();
      });
    });

    it('handles entity deletion failure gracefully', async () => {
      const mockDeleteEntity = vi.fn().mockRejectedValue(new Error('Deletion failed'));
      vi.mocked(storeApi.useDeleteEntityMutation).mockReturnValue([
        vi.fn().mockImplementation(() => ({ unwrap: mockDeleteEntity })),
        { isLoading: false, isError: true },
      ] as any);

      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByLabelText('Delete');
      await userEvent.click(deleteButtons[0]);

      const confirmButton = screen.getByText('chatbot.yesDelete');
      await userEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockDeleteEntity).toHaveBeenCalled();
      });
    });
  });

  describe('Table Styling and Layout', () => {
    it('applies correct styling to table rows', () => {
      render(<EntitiesTab />);

      const tableRows = screen.getAllByRole('row');
      // Skip header row
      const dataRows = tableRows.slice(1);

      // Check alternating row colors
      expect(dataRows[0]).toHaveClass('bg-tertiary-100');
      expect(dataRows[1]).not.toHaveClass('bg-tertiary-100');
      expect(dataRows[2]).toHaveClass('bg-tertiary-100');
    });

    it('displays action buttons correctly', () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByLabelText('Edit');
      const deleteButtons = screen.getAllByLabelText('Delete');

      expect(editButtons).toHaveLength(3);
      expect(deleteButtons).toHaveLength(3);
    });

    it('handles different entity types correctly', () => {
      render(<EntitiesTab />);

      // Check that all entity types are displayed
      expect(screen.getByText('TEXT')).toBeInTheDocument();
      expect(screen.getByText('EMAIL')).toBeInTheDocument();
      expect(screen.getByText('REGEX')).toBeInTheDocument();
    });
  });

  describe('Form Validation Edge Cases', () => {
    it('validates entity name length', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]);

      const nameInput = screen.getByLabelText('Entity name');
      await userEvent.type(nameInput, 'a'.repeat(50)); // Exceeds max length

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      // Form should still be open due to validation error
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('shows form elements correctly', async () => {
      render(<EntitiesTab />);

      const addButtons = screen.getAllByText('entities.addTitle');
      await userEvent.click(addButtons[0]);

      // Initially no regex input should be visible (default is TEXT type)
      expect(screen.queryByLabelText('Regex value')).not.toBeInTheDocument();

      // Basic form elements should be present
      expect(screen.getByLabelText('Entity name')).toBeInTheDocument();
      expect(screen.getByLabelText('Select type')).toBeInTheDocument();
    });
  });

  describe('Integration with EntityHighlightInput', () => {
    it('provides entities for entity highlighting functionality', () => {
      render(<EntitiesTab />);

      // Verify that entities are loaded and available
      expect(screen.getByText('PersonName')).toBeInTheDocument();
      expect(screen.getByText('EmailAddress')).toBeInTheDocument();
      expect(screen.getByText('PhonePattern')).toBeInTheDocument();
    });
  });
});
