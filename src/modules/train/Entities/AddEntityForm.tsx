import React from 'react';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';
import { useCreateEntityMutation, useUpdateEntityMutation } from '@/store/api';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';

import RenderButtons from '../components/RenderButtons';
import { EntityType, Entity } from '@/types';
import { createEntitySchema, EntityFormValues } from '../schema';

interface AddEntityFormProps {
  onClose: () => void;
  botId: string;
  entity?: Entity;
}

const AddEntityForm: React.FC<AddEntityFormProps> = ({ onClose, botId, entity }) => {
  const { t } = useTranslation();
  const [createEntity] = useCreateEntityMutation();
  const [updateEntity] = useUpdateEntityMutation();
  const { toast } = useToast();

  const form = useForm<EntityFormValues>({
    resolver: zodResolver(createEntitySchema(t)),
    defaultValues: {
      name: entity?.name || '',
      type: entity?.type || EntityType.TEXT,
      metadata: entity?.metadata || {},
    },
  });

  const entityType = form.watch('type');

  const onSubmit = async (values: EntityFormValues) => {
    try {
      const commonPayload = {
        name: values.name,
        type: values.type,
        metadata: values.metadata,
      };

      if (entity) {
        await updateEntity({
          id: entity.id,
          ...commonPayload,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('entities.entityUpdated')} />,
        });
      } else {
        await createEntity({
          botId,
          ...commonPayload,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('entities.entityAdded')} />,
        });
      }
      onClose();
      form.reset();
    } catch (error: any) {
      console.error(`Failed to ${entity ? 'update' : 'create'} entity:`, error);
      toast({
        title: t('common.error'),
        description: error?.data?.error?.message || t('common.somethingWrong'),
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex gap-4 mb-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <FloatingField
                    id="entityName"
                    label={t('entities.entityNamePlaceholder')}
                    aria-label="Entity name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <FloatingField
                    id="entityType"
                    label={t('entities.selectType')}
                    className="capitalize h-full"
                    as={FloatingType.SELECT}
                    aria-label="Select type"
                    options={Object.values(EntityType).map(type => ({
                      label: type.toLowerCase(),
                      value: type,
                      className: 'capitalize',
                    }))}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {entityType === EntityType.REGEX && (
          <FormField
            control={form.control}
            name="metadata.value"
            render={({ field }) => (
              <FormItem className="pt-2">
                <FormControl>
                  <FloatingField
                    id="regexValue"
                    label={t('entities.regexValuePlaceholder')}
                    aria-label="Regex value"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <RenderButtons
          handleClose={onClose}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!entity}
        />
      </form>
    </Form>
  );
};

export default AddEntityForm;
