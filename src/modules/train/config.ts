import IntentUtterancesTab from './IntentUtterances';
import EntitiesTab from './Entities';
import FAQsTab from './FAQs';
import SynonymsTab from './Synonyms';
import SmallTalkTab from './SmallTalk';
import TrainFromLogsTab from './TrainFromLogs';
import { TabConfig } from '@/types';

export const trainSubTabs: TabConfig[] = [
  { id: 'Intent Utterances', labelKey: 'train.tabs.intentUtterances', Component: IntentUtterancesTab },
  {
    id: 'Entities',
    labelKey: 'train.tabs.entities',
    Component: EntitiesTab,
    className: 'bg-tertiary-50',
  },
  { id: 'FAQs', labelKey: 'train.tabs.faqs', Component: FAQsTab },
  { id: 'Synonyms', labelKey: 'train.tabs.synonyms', Component: SynonymsTab },
  { id: 'Small Talk', labelKey: 'train.tabs.smallTalk', Component: SmallTalkTab },
  { id: 'Train from Logs', labelKey: 'train.tabs.trainFromLogs', Component: TrainFromLogsTab },
];
