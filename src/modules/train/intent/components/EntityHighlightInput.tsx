import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { Entity } from '@/types';
import { useGetEntitiesQuery } from '@/store/api';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

// Types
interface EntityHighlightInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  botId: string;
  intentId?: string;
  autoFocus?: boolean;
}

interface ParsedEntity {
  start: number;
  end: number;
  text: string;
  name: string;
  id: string;
  type: string;
}

interface HoveredEntity {
  id: string;
  name: string;
  type: string;
  x: number;
  y: number;
}

interface SelectionState {
  text: string;
  start: number;
  end: number;
}

interface HistoryState {
  undo: string[];
  redo: string[];
}

//TODO: this component is getting too big. Should be split into smaller components and need to fix all existing bugs in it

// Utility Functions
const parseEntities = (text: string): ParsedEntity[] => {
  const entityRegex = /\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g;
  const entities: ParsedEntity[] = [];
  let match;
  while ((match = entityRegex.exec(text)) !== null) {
    entities.push({
      start: match.index,
      end: match.index + match[0].length,
      text: match[1],
      name: match[2],
      id: match[3],
      type: match[4],
    });
  }
  return entities.sort((a, b) => a.start - b.start);
};

const getDisplayText = (text: string): string =>
  text.replace(/\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g, '$1');

const colors = {
  TEXT: 'bg-primary-100 text-primary-800 border-primary-200',
  EMAIL: 'bg-success-100 text-success-800 border-success-200',
  DATE: 'bg-purple-100 text-purple-800 border-purple-200',
  NUMBER: 'bg-orange-100 text-orange-800 border-orange-200',
  REGEX: 'bg-pink-100 text-pink-800 border-pink-200',
};

const getEntityColor = (type: string): string => {
  return (
    colors[type as keyof typeof colors] || 'bg-tertiary-100 text-tertiary-800 border-tertiary-200'
  );
};

const reconcileTextWithEntities = (newText: string, oldValue: string): string => {
  const oldEntities = parseEntities(oldValue);
  if (oldEntities.length === 0) return newText;

  const displayEntities = oldEntities.map(entity => ({
    ...entity,
    displayStart: getDisplayText(oldValue.substring(0, entity.start)).length,
    displayEnd: getDisplayText(oldValue.substring(0, entity.start)).length + entity.text.length,
  }));

  let newValue = '';
  let currentPos = 0;
  for (const entity of displayEntities) {
    if (
      entity.displayStart <= newText.length &&
      newText.substring(entity.displayStart, entity.displayEnd) === entity.text
    ) {
      if (entity.displayStart > currentPos) {
        newValue += newText.substring(currentPos, entity.displayStart);
      }
      newValue += `[${entity.text}](${entity.name}_${entity.id}_${entity.type})`;
      currentPos = entity.displayEnd;
    }
  }
  if (currentPos < newText.length) {
    newValue += newText.substring(currentPos);
  }

  return newValue;
};

const EntityTooltip: React.FC<{
  entity: HoveredEntity;
  onRemove: (id: string) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}> = ({ entity, onRemove, onMouseEnter, onMouseLeave }) => {
  const { t } = useTranslation();
  return (
    <div
      className="absolute z-50 bg-white border border-tertiary-200 rounded-md shadow-lg p-3 text-xs min-w-[120px]"
      style={{
        left: Math.max(10, Math.min(entity.x, window.innerWidth - 150)),
        top: entity.y - 50,
        transform: 'translateX(-50%)',
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="flex items-center justify-between gap-2 mb-1">
        <span className="font-medium text-tertiary-900">{entity.name}</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(entity.id)}
          className="h-5 w-5 p-0 text-error-500 hover:text-error-700 hover:bg-error-50"
          title={t('entities.removeEntity')}
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
      <div className="text-tertiary-500">{entity.type}</div>
      <div
        className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-tertiary-200"
        style={{ marginTop: '-1px' }}
      />
    </div>
  );
};

const EntityDropdown: React.FC<{
  entities: Entity[];
  selectedText: string;
  onSelect: (entity: Entity) => void;
  onClose: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}> = ({ entities, selectedText, onSelect, onClose, searchTerm, setSearchTerm }) => {
  const { t } = useTranslation();
  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-tertiary-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
      <div className="p-2 border-b border-tertiary-100">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-tertiary-400" />
          <Input
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder={t('entities.searchEntities')}
            className="pl-8 pr-8 h-8"
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        {selectedText && (
          <div className="mt-2 text-xs text-tertiary-600">
            {t('entities.selected')}: &ldquo;<span className="font-medium">{selectedText}</span>
            &rdquo;
          </div>
        )}
      </div>
      <div className="max-h-40 overflow-y-auto">
        {entities.length === 0 ? (
          <div className="p-3 text-sm text-tertiary-500 text-center">
            {t('entities.noEntitiesFound')}
          </div>
        ) : (
          entities.map(entity => (
            <button
              key={entity.id}
              onClick={() => onSelect(entity)}
              className="w-full text-left p-3 hover:bg-tertiary-50 border-b border-tertiary-50 last:border-b-0 focus:outline-none focus:bg-tertiary-50"
            >
              <div className="font-medium text-sm">{entity.name}</div>
              <div className="text-xs text-tertiary-500">{entity.type}</div>
            </button>
          ))
        )}
      </div>
    </div>
  );
};

// Main Component
const EntityHighlightInput: React.FC<EntityHighlightInputProps> = ({
  value,
  onChange,
  placeholder,
  className,
  botId,
  intentId,
  autoFocus,
}) => {
  const { t } = useTranslation();
  const defaultPlaceholder = placeholder || t('intents.utterances.enterPlaceholder');
  const [selection, setSelection] = useState<SelectionState>({ text: '', start: 0, end: 0 });
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredEntity, setHoveredEntity] = useState<HoveredEntity | null>(null);
  const [error, setError] = useState<string>('');
  const [history, setHistory] = useState<HistoryState>({ undo: [], redo: [] });
  const [isTyping, setIsTyping] = useState(false);
  const contentEditableRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInternalUpdateRef = useRef(false);
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTooltipHoveredRef = useRef(false);

  useEffect(() => {
    if (autoFocus && contentEditableRef.current) {
      contentEditableRef.current.focus();
    }
  }, [autoFocus]);

  const { data: entitiesResponse, isLoading } = useGetEntitiesQuery({ botId, page: 1, limit: 100 });
  const filteredEntities = useMemo(
    () =>
      entitiesResponse?.data?.items.filter(
        (entity: Entity) =>
          (entity.intentId === null || entity.intentId === intentId) &&
          (searchTerm === '' ||
            entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entity.type.toLowerCase().includes(searchTerm.toLowerCase()))
      ) || [],
    [entitiesResponse, intentId, searchTerm]
  );

  const checkEntityOverlap = useCallback(
    (
      start: number,
      end: number,
      entities: ParsedEntity[]
    ): { hasOverlap: boolean; conflictingEntity?: ParsedEntity } => {
      const displayText = getDisplayText(value);
      for (const entity of entities) {
        const entityDisplayStart = getDisplayText(value.substring(0, entity.start)).length;
        const entityDisplayEnd = entityDisplayStart + entity.text.length;
        if (
          (start < entityDisplayEnd && end > entityDisplayStart) ||
          (start >= entityDisplayStart && start < entityDisplayEnd) ||
          (end > entityDisplayStart && end <= entityDisplayEnd) ||
          (start <= entityDisplayStart && end >= entityDisplayEnd)
        ) {
          return { hasOverlap: true, conflictingEntity: entity };
        }
      }
      return { hasOverlap: false };
    },
    [value]
  );

  const renderContent = useCallback(() => {
    const entities = parseEntities(value);
    const displayText = getDisplayText(value);
    if (entities.length === 0) return displayText;

    const parts: string[] = [];
    let lastEnd = 0;
    const displayEntities = entities.map(entity => {
      const displayStart = getDisplayText(value.substring(0, entity.start)).length;
      return { ...entity, displayStart, displayEnd: displayStart + entity.text.length };
    });

    displayEntities.forEach(entity => {
      if (entity.displayStart > lastEnd) {
        parts.push(displayText.substring(lastEnd, entity.displayStart));
      }
      const color = getEntityColor(entity.type);
      parts.push(
        `<span class="${color} border rounded px-1 cursor-pointer inline-block whitespace-nowrap" data-entity-id="${entity.id}" data-entity-name="${entity.name}" data-entity-type="${entity.type}">${entity.text}</span>`
      );
      lastEnd = entity.displayEnd;
    });

    if (lastEnd < displayText.length) {
      parts.push(displayText.substring(lastEnd));
    }
    return parts.join('');
  }, [value]);

  const saveCursor = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) {
      return { position: 0, node: null, offset: 0 };
    }
    const range = selection.getRangeAt(0);
    if (!contentEditableRef.current.contains(range.startContainer)) {
      return { position: 0, node: null, offset: 0 };
    }
    const beforeRange = range.cloneRange();
    beforeRange.selectNodeContents(contentEditableRef.current);
    beforeRange.setEnd(range.startContainer, range.startOffset);
    return {
      position: beforeRange.toString().length,
      node: range.startContainer,
      offset: range.startOffset,
    };
  }, []);

  const restoreCursor = useCallback(
    (saved: { position: number; node: Node | null; offset: number }) => {
      if (!contentEditableRef.current) return;
      const { position, node, offset } = saved;

      try {
        if (node && contentEditableRef.current.contains(node) && node.nodeType === Node.TEXT_NODE) {
          const range = document.createRange();
          range.setStart(node, Math.min(offset, node.textContent?.length || 0));
          range.collapse(true);
          window.getSelection()?.removeAllRanges();
          window.getSelection()?.addRange(range);
          return;
        }

        if (position > 0) {
          const walker = document.createTreeWalker(
            contentEditableRef.current,
            NodeFilter.SHOW_TEXT
          );
          let currentPos = 0;
          let textNode = walker.nextNode();
          while (textNode) {
            const nodeText = textNode.textContent || '';
            const nodeEnd = currentPos + nodeText.length;
            if (position <= nodeEnd) {
              const range = document.createRange();
              range.setStart(textNode, Math.min(position - currentPos, nodeText.length));
              range.collapse(true);
              window.getSelection()?.removeAllRanges();
              window.getSelection()?.addRange(range);
              break;
            }
            currentPos = nodeEnd;
            textNode = walker.nextNode();
          }
        }
      } catch (error) {
        console.warn('Failed to restore cursor:', error);
      }
    },
    []
  );

  const handleUndo = useCallback(() => {
    if (history.undo.length === 0) return;
    setHistory(prev => ({
      undo: prev.undo.slice(0, -1),
      redo: [...prev.redo, value],
    }));
    onChange(history.undo[history.undo.length - 1]);
  }, [history, value, onChange]);

  const handleRedo = useCallback(() => {
    if (history.redo.length === 0) return;
    setHistory(prev => ({
      undo: [...prev.undo, value],
      redo: prev.redo.slice(0, -1),
    }));
    onChange(history.redo[history.redo.length - 1]);
  }, [history, value, onChange]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        if (e.key === 'z' && !e.shiftKey) {
          e.preventDefault();
          handleUndo();
        } else if ((e.key === 'z' && e.shiftKey) || e.key === 'y') {
          e.preventDefault();
          handleRedo();
        }
        return;
      }

      if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) return;

        const range = selection.getRangeAt(0);
        let node = range.startContainer;
        let offset = range.startOffset;

        contentEditableRef.current.normalize();

        let entitySpan: HTMLElement | null = null;
        let currentNode: Node | null =
          node.nodeType === Node.ELEMENT_NODE ? node : node.parentElement;
        while (currentNode && currentNode !== contentEditableRef.current) {
          if (currentNode.nodeName === 'SPAN' && (currentNode as HTMLElement).dataset.entityId) {
            entitySpan = currentNode as HTMLElement;
            break;
          }
          currentNode = currentNode.parentElement;
        }

        if (entitySpan) {
          const textNode = entitySpan.firstChild as Text;
          const textLength = textNode?.length || 0;

          if (e.key === 'ArrowRight' && node === textNode && offset === textLength) {
            e.preventDefault();
            const newRange = document.createRange();
            newRange.setStartAfter(entitySpan);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          } else if (e.key === 'ArrowLeft' && node === textNode && offset === 0) {
            e.preventDefault();
            const newRange = document.createRange();
            newRange.setStartBefore(entitySpan);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } else {
          if (e.key === 'ArrowRight' && node.nodeType === Node.TEXT_NODE) {
            const nextSibling = node.nextSibling;
            if (nextSibling?.nodeName === 'SPAN' && (nextSibling as HTMLElement).dataset.entityId) {
              if (offset === node.textContent?.length) {
                e.preventDefault();
                const newRange = document.createRange();
                newRange.setStartAfter(nextSibling);
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);
              }
            }
          } else if (e.key === 'ArrowLeft' && node.nodeType === Node.TEXT_NODE) {
            const previousSibling = node.previousSibling;
            if (
              previousSibling?.nodeName === 'SPAN' &&
              (previousSibling as HTMLElement).dataset.entityId
            ) {
              if (offset === 0) {
                e.preventDefault();
                const newRange = document.createRange();
                newRange.setStartBefore(previousSibling);
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);
              }
            }
          }
        }
      }
    },
    [handleUndo, handleRedo]
  );

  const handleContentChange = useCallback(() => {
    if (!contentEditableRef.current) return;
    setIsTyping(true);

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    typingTimeoutRef.current = setTimeout(() => setIsTyping(false), 500);

    const newText = contentEditableRef.current.textContent || '';
    const newValue = reconcileTextWithEntities(newText, value);
    if (newValue !== value) {
      setHistory(prev => ({ ...prev, undo: [...prev.undo.slice(-19), value], redo: [] }));
      isInternalUpdateRef.current = true;
      onChange(newValue);
    }
  }, [value, onChange]);

  const handleSelection = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) {
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      return;
    }

    contentEditableRef.current.normalize();

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString().trim();
    if (selectedText && contentEditableRef.current.contains(range.commonAncestorContainer)) {
      const beforeRange = range.cloneRange();
      beforeRange.selectNodeContents(contentEditableRef.current);
      beforeRange.setEnd(range.startContainer, range.startOffset);
      const start = beforeRange.toString().length;
      const end = start + selectedText.length;

      setSelection({ text: selectedText, start, end });
      setShowDropdown(true);
      setSearchTerm('');
    } else {
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
    }
  }, []);

  const handleEntitySelect = useCallback(
    (entity: Entity) => {
      if (!selection.text) return;
      const entities = parseEntities(value);
      const overlap = checkEntityOverlap(selection.start, selection.end, entities);

      if (overlap.hasOverlap) {
        setError(
          `Cannot assign "${entity.name}" to "${selection.text}". Overlaps with "${overlap.conflictingEntity?.name}".`
        );
        setTimeout(() => setError(''), 5000);
        return;
      }

      const displayText = getDisplayText(value);
      const entityMarkup = `[${selection.text}](${entity.name}_${entity.id}_${entity.type})`;
      let newValue = '';

      if (entities.length === 0) {
        newValue =
          displayText.substring(0, selection.start) +
          entityMarkup +
          displayText.substring(selection.end);
      } else {
        const parts = [];
        let currentPos = 0;
        const allParts = [
          ...entities.map(e => ({
            start: getDisplayText(value.substring(0, e.start)).length,
            end: getDisplayText(value.substring(0, e.start)).length + e.text.length,
            content: `[${e.text}](${e.name}_${e.id}_${e.type})`,
          })),
          { start: selection.start, end: selection.end, content: entityMarkup },
        ].sort((a, b) => a.start - b.start);

        for (const part of allParts) {
          if (part.start > currentPos) {
            parts.push(displayText.substring(currentPos, part.start));
          }
          parts.push(part.content);
          currentPos = part.end;
        }
        if (currentPos < displayText.length) {
          parts.push(displayText.substring(currentPos));
        }
        newValue = parts.join('');
      }

      setHistory(prev => ({ ...prev, undo: [...prev.undo.slice(-19), value], redo: [] }));
      onChange(newValue);
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      window.getSelection()?.removeAllRanges();
    },
    [value, selection, onChange, checkEntityOverlap]
  );

  const removeEntity = useCallback(
    (id: string) => {
      const newValue = value.replace(
        new RegExp(`\\[([^\\]]+)\\]\\([^_]+_${id}_[^)]+\\)`, 'g'),
        '$1'
      );
      setHistory(prev => ({ ...prev, undo: [...prev.undo.slice(-19), value], redo: [] }));
      onChange(newValue);
      setHoveredEntity(null);
      if (contentEditableRef.current) {
        isInternalUpdateRef.current = false;
        contentEditableRef.current.innerHTML = renderContent();
      }
    },
    [value, onChange, renderContent]
  );

  const handleHover = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.dataset.entityId && contentEditableRef.current) {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
      const rect = target.getBoundingClientRect();
      const containerRect = contentEditableRef.current.getBoundingClientRect();
      setHoveredEntity({
        id: target.dataset.entityId,
        name: target.dataset.entityName || '',
        type: target.dataset.entityType || '',
        x: rect.left + rect.width / 2 - containerRect.left,
        y: rect.top - containerRect.top - 10,
      });
    } else if (!isTooltipHoveredRef.current) {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
      tooltipTimeoutRef.current = setTimeout(() => setHoveredEntity(null), 100);
    }
  }, []);

  const handleTooltipMouseEnter = useCallback(() => {
    isTooltipHoveredRef.current = true;
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
  }, []);

  const handleTooltipMouseLeave = useCallback(() => {
    isTooltipHoveredRef.current = false;
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
    tooltipTimeoutRef.current = setTimeout(() => setHoveredEntity(null), 100);
  }, []);

  useEffect(() => {
    if (contentEditableRef.current && !isTyping && !isInternalUpdateRef.current) {
      const cursor = saveCursor();
      contentEditableRef.current.innerHTML = renderContent();
      restoreCursor(cursor);
    }
    isInternalUpdateRef.current = false;
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, [value, isTyping, renderContent, saveCursor, restoreCursor]);

  if (isLoading) {
    return (
      <div
        className={cn(
          'min-h-[40px] border border-tertiary-200 rounded-md p-2 bg-tertiary-50',
          className
        )}
      >
        <div className="text-tertiary-500 text-sm">{t('entities.loading')}</div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <div
        ref={contentEditableRef}
        contentEditable
        onInput={handleContentChange}
        onMouseUp={handleSelection}
        onKeyUp={handleSelection}
        onKeyDown={handleKeyDown}
        onMouseOver={handleHover}
        onMouseLeave={handleHover}
        className="w-full h-[40px] px-3 py-2 border border-tertiary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white overflow-x-auto overflow-y-hidden scrollbar-thin"
        style={{ fontSize: '14px', lineHeight: '1.5', whiteSpace: 'nowrap' }}
        data-placeholder={defaultPlaceholder}
        role="textbox"
        aria-label="Enter utterance"
      />
      {!value && (
        <div className="absolute inset-0 px-3 py-2 text-tertiary-400 pointer-events-none flex items-center text-sm">
          {defaultPlaceholder}
        </div>
      )}
      {error && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-error-50 border border-error-200 rounded-md text-error-700 text-xs z-40">
          <div className="flex items-start gap-2">
            <X className="h-3 w-3 text-error-500 mt-0.5" />
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setError('')}
              className="ml-auto h-4 w-4 p-0 text-error-500 hover:text-error-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
      {showDropdown && (
        <EntityDropdown
          entities={filteredEntities}
          selectedText={selection.text}
          onSelect={handleEntitySelect}
          onClose={() => {
            setShowDropdown(false);
            setSelection({ text: '', start: 0, end: 0 });
            setSearchTerm('');
          }}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
      )}
      {hoveredEntity && (
        <EntityTooltip
          entity={hoveredEntity}
          onRemove={removeEntity}
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        />
      )}
    </div>
  );
};

export default EntityHighlightInput;
