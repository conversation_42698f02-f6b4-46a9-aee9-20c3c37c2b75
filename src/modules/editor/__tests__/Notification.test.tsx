import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import Notification from '../PluginForms/notification'; // adjust import path
import { describe, expect, it } from 'vitest';

// Utility wrapper with react-hook-form context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      process: {
        channelData: {
          web: {
            english: {
              notificationChannel: [],
              senderId: '',
              recipientMSISDN: '',
              recipientEmail: '',
              emailSubject: '',
              commonMessage: '',
              emailAddresses: [],
            },
          },
        },
      },
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('Notification Component', () => {
  it('renders channel selection dropdown', () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );
    expect(screen.getByLabelText('Select Notification Channel')).toBeInTheDocument();
  });

  it('shows Email-specific fields when Email is selected', async () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );

    const channelDropdown = screen.getByLabelText('Select Notification Channel');

    // Simulate multi-select change
    fireEvent.change(channelDropdown, { target: { value: ['email'] } });

    await waitFor(() => {
      expect(screen.getByText(/Configure Recipient Email/i)).toBeInTheDocument();
      expect(screen.getByLabelText('Enter Recipient Email Address')).toBeInTheDocument();
    });
  });

  it('allows configuring email fields and retains values after SAVE', async () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );

    const channelDropdown = screen.getByLabelText('Select Notification Channel');
    fireEvent.change(channelDropdown, { target: { value: ['email'] } });

    const emailInput = await screen.findByLabelText('Enter Recipient Email Address');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    const subjectInput = screen.getByLabelText('Enter Subject of the Email');
    fireEvent.change(subjectInput, { target: { value: 'Test Subject' } });

    const messageInput = screen.getByLabelText('Configure the common message for SMS and Email');
    fireEvent.change(messageInput, { target: { value: 'Test message' } });

    fireEvent.click(screen.getByText(/SAVE/i));

    expect(emailInput).toHaveValue('<EMAIL>');
    expect(subjectInput).toHaveValue('Test Subject');
    expect(messageInput).toHaveValue('Test message');
  });

  it('clears email fields when Email is deselected', async () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );

    const channelDropdown = screen.getByLabelText('Select Notification Channel');
    fireEvent.change(channelDropdown, { target: { value: ['email'] } });

    const emailInput = await screen.findByLabelText('Enter Recipient Email Address');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Simulate deselection
    fireEvent.change(channelDropdown, { target: { value: [] } });

    await waitFor(() => {
      expect(emailInput).toHaveValue('');
    });
  });
});
