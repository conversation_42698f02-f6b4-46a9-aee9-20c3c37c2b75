import React from 'react';
import NodeItem from './NodeItem';
import useNodeConfig from '@/modules/editor/hooks/useStencilNodeConfig';

interface NodeListProps {
  collapseStatus: boolean;
  currentTab: string;
  search: string;
  startDrag: (event: React.PointerEvent, type: string) => void;
}

const NodeList: React.FC<NodeListProps> = ({ collapseStatus, currentTab, search, startDrag }) => {
  const { enabledNodes, getNodes, getComingSoonNodes } = useNodeConfig();

  return (
    <div className="h-auto max-h-[45vh] overflow-y-auto overflow-x-hidden px-5">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-8 py-5 justify-center">
        {collapseStatus ? (
          Object.keys(enabledNodes).map(category =>
            enabledNodes[category].map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} />
            ))
          )
        ) : (
          <>
            {getNodes(currentTab, search).map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} />
            ))}
            {getComingSoonNodes(currentTab, search).map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} isComingSoon />
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default NodeList;
