import { dia } from 'rappid';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import Stencil from './widget/Stencil/index';

import {
  useGetApplicationDetailsQuery,
  useUpdateApplicationDetailsMutation,
} from '@/store/api/studioApi';
import { useEditor } from './hooks/useEditor';
import { joinToLeapJSON } from './utils/jointJsToLeap';
import { leapToJointJSON } from './utils/leapToJointJs';
import FlowsPanel from './widget/flowsPanel';
import { useUndoRedo } from '@/modules/editor/hooks/useUndoRedo';
import useNodeHandler from '@/modules/editor/hooks/useEditorNodeHandler';
import Form from './NodeConfigSheet';
import { useFormHandler } from './hooks/useFormHandler';
import { SettingDetails } from './types';
import { useAutoSave } from './hooks/useAutoSave';

interface IProps {
  id: string;
  jsonDetails?: any;
}

const isStencilEnabled = true;
const isSimulatorEnabled = false;
// const id = 'some-database-id';
const NeuraTalkEditor = ({ id, jsonDetails }: IProps) => {
  const [settingDetails, setSettingDetails] = useState<SettingDetails>({});
  const [selectedAppId, setSelectedAppId] = useState<string>(id); // <-- add this
  const [isEditorLoading, setEditorLoading] = useState(false);
  const [error, setError] = useState(null);
  const canvasWithPallette = useRef<HTMLDivElement>(null);
  const isEdit = true;

  const {
    data: applicationData,
    error: fetchError,
    isLoading,
  } = useGetApplicationDetailsQuery({ appId: id });

  const changeJointJsonToLeapOnDrop = useCallback(
    (graph: dia.Graph, updateData = null) => {
      //TODO: add Proper Type
      if (graph) {
        const data = joinToLeapJSON(
          JSON.parse(JSON.stringify(graph.toJSON())),
          updateData || settingDetails,
          graph
        ).modules;

        const { nodes, jsonData } = leapToJointJSON(data);

        graph.fromJSON(nodes);

        setSettingDetails(prev => {
          return {
            ...prev,
            ...jsonData,
          };
        });
      }
    },
    [settingDetails, setSettingDetails]
  );

  const {
    canvas,
    graphInstance,
    paperInstance,
    initializeEditor,
    loadApplicationData,
    setAppDetails,
    autoUpdateHandler,
    modalTypeDetails,
    setModalTypeDetails,
    scrollInstance,
    currentElementView,
    setCurrentElementView,
  } = useEditor({
    canvasWithPallette,
    isEdit,
    changeJointJsonToLeapOnDrop,
    id,
    settingDetails,
    setSettingDetails,
    setEditorLoading,
  });

  const { handleFormClose } = useFormHandler({
    setModalTypeDetails,
    settingDetails,
    scrollInstance,
    graphInstance,
    currentElementView,
    setCurrentElementView,
  });

  const { updateCurrentDetails, removeLastUndoState, updateInitialState, currentIndex, lastIndex } =
    useUndoRedo();

  const { handleNodeAdd, isMaxNodeReached, handleSettingsUpdate } = useNodeHandler({
    graphInstance,
    changeJointJsonToLeapOnDrop,
    autoUpdateHandler,
    settingDetails,
    setError,
    setSettingDetails,
    modalTypeDetails,
  });

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < lastIndex;

  useEffect(() => {
    const cleanup = initializeEditor();
    return () => {
      initialUpdateRef.current = '';
      cleanup?.();
    };
  }, []);

  const initialUpdateRef = useRef('');

  useEffect(() => {
    if (!graphInstance || !paperInstance) return;
    if (jsonDetails) {
      setEditorLoading(true);
      updateInitialState({ payload: jsonDetails, action: 'initial' });
      setAppDetails(jsonDetails as any);
      loadApplicationData(jsonDetails as any);
    } else if (applicationData && id != initialUpdateRef.current) {
      setEditorLoading(true);
      initialUpdateRef.current = id;
      //TODO: fix this
      updateInitialState({
        payload: applicationData as any,
        action: 'initial',
      });
      setAppDetails(applicationData as any);
      loadApplicationData(applicationData as any);
    }
  }, [applicationData, graphInstance, paperInstance]);

  return (
    <div className="studio-root">
      <div
        className="canvas-wrapper min-h-screen full-screen"
        ref={canvasWithPallette}
        style={{ position: 'relative', height: 'calc(100% - 80px)' }}
      >
        <div className="canvas" ref={canvas}>
          <div id="react-portal-modal-container" />
        </div>
        {isStencilEnabled && (
          <div>
            {paperInstance && isEdit && (
              <Stencil
                paper={paperInstance}
                graph={graphInstance as any}
                handleNodeAdd={handleNodeAdd}
                canvasWithPallette={canvasWithPallette as any}
                isMaxNodeReached={isMaxNodeReached}
              />
            )}
          </div>
        )}

        {modalTypeDetails && settingDetails[modalTypeDetails?.id] && !isSimulatorEnabled && (
          <Form
            handleClose={handleFormClose}
            type={modalTypeDetails?.type}
            id={modalTypeDetails?.id}
            modalTypeDetails={modalTypeDetails}
            // startUrl={startUrl}
            moduleData={settingDetails[modalTypeDetails?.id]}
            handleSave={handleSettingsUpdate} //TODO: need to add proper type
            isEdit={isEdit}
            // isPublishedEnabled={isPublishedEnabled}
          />
        )}

        {/* <ErrorModal
          details={error}
          open={!!error}
          onClose={() => {
            updateError(null);
          }}
        />
        <ConfirmBox
          open={publishConfirmStatus}
          onSuccess={updateAppJson}
          onClose={() => {
            updateConfirmStatus(false);
          }}
        /> */}
        {/* {isNodeModalOpen && (
          <AutoSuggestion
            onClose={toggleNodeModal}
            graph={graphInstance}
            autoSuggestionPosition={autoSuggestionPosition}
            changeJointJsonToLeapOnDrop={changeJointJsonToLeapOnDrop}
            isMaxNodeReached={isMaxNodeReached}
          />
        )} */}
        {/* <SuccessModal
          open={publishSuccessModal}
          onClose={() => {
            updatePublishSuccessModal(false);
          }}
          title={appDetails?.name}
        /> */}
      </div>
    </div>
  );
};

export default NeuraTalkEditor;
