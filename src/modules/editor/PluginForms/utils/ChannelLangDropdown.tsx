import DropdownButton from '@/components/dropdownButton';
import { useFormContext } from 'react-hook-form';
import { platformOptions, PlatformType } from '@/lib/constant';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import LanguageDropdown from '@/components/LanguageDropdown';

export default function ChannelLangDropdown() {
  const { control } = useFormContext();

  return (
    <>
      <div className="flex items-center px-4 space-x-2">
        <FormField
          control={control}
          name="settings.language"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LanguageDropdown
                  onChange={(_, node) => field.onChange(node.name.toLowerCase())}
                  initialValue={field.value}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="settings.platform"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DropdownButton
                  value={field.value ?? PlatformType.Web}
                  onChange={field.onChange}
                  options={platformOptions}
                  className="min-w-24"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </>
  );
}
