import React from 'react';
import { useFormContext } from 'react-hook-form';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import ChannelLangDropdown from './utils/ChannelLangDropdown';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useTranslation } from 'react-i18next';
import { NotificationChannel, Option } from '../types/notificationOptions';

const notificationChannelOptions: Option[] = [
  { id: '1', value: NotificationChannel.SMS, label: 'SMS' },
  { id: '2', value: NotificationChannel.EMAIL, label: 'Email' },
];

const senderIdOptions: Option[] = [
  { id: '1', value: 'ae-50507', label: 'AE-50507' },
  { id: '2', value: 'vi-hawk', label: 'VI-HAWK' },
];

const emailOptions: Option[] = [
  { id: '1', value: '<EMAIL>', label: '<EMAIL>' },
  { id: '2', value: '<EMAIL>', label: '<EMAIL>' },
];

const Notification: React.FC = () => {
  const { control, watch } = useFormContext();
  const { t } = useTranslation();
  const language = watch('settings.language');
  const platform = watch('settings.platform');
  const prefixKey = `process.channelData.${platform}.${language}`;
  const selectedChannels = watch(`${prefixKey}.notificationChannel`) || [];

  return (
    <div className="flex flex-col px-3 space-y-2 gap-4">
      <ChannelLangDropdown />
      <FormField
        control={control}
        name={`${prefixKey}.notificationChannel`}
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                label={t('notification.notificationChannel')}
                as={FloatingType.MULTI_SELECT}
                value={notificationChannelOptions.filter(opt => field.value?.includes(opt.value))}
                onChange={(selectedOptions: Option[]) => {
                  field.onChange(selectedOptions.map(opt => opt.value));
                }}
                options={notificationChannelOptions}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {selectedChannels.length > 0 && (
        <FormField
          control={control}
          name={`${prefixKey}.commonMessage`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  maxLength={350}
                  label={t('notification.configureMessage')}
                  as={FloatingType.TEXTAREA}
                  value={field.value || ''}
                  onChange={field.onChange}
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      {selectedChannels.includes(NotificationChannel.SMS) && (
        <div className="w-full flex flex-col gap-3 border p-4 rounded">
          <h2 className="text-lg font-semibold">{t('notification.configureSMS')}</h2>
          <FormField
            control={control}
            name={`${prefixKey}.senderId`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    label={t('notification.selectSenderID')}
                    as={FloatingType.SELECT}
                    value={field.value || ''}
                    onChange={field.onChange}
                    options={senderIdOptions}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`${prefixKey}.recipientMSISDN`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    label={t('notification.recipientMSISDN')}
                    as={FloatingType.SELECT}
                    value={field.value || ''}
                    onChange={field.onChange}
                    type="text"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )}
      {selectedChannels.includes(NotificationChannel.EMAIL) && (
        <div className="w-full flex flex-col gap-3 border p-4 rounded">
          <h2 className="text-lg font-semibold">{t('notification.configureEmail')}</h2>
          <FormField
            control={control}
            name={`${prefixKey}.emailAddresses`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    label={t('notification.selectEmail')}
                    as={FloatingType.SELECT}
                    value={field.value || ''}
                    onChange={field.onChange}
                    options={emailOptions}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`${prefixKey}.recipientEmail`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    label={t('notification.recipientEmail')}
                    as={FloatingType.INPUT}
                    value={field.value || ''}
                    onChange={field.onChange}
                    type="email"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`${prefixKey}.emailSubject`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    label={t('notification.enterSubjectOfEmail')}
                    as={FloatingType.INPUT}
                    value={field.value || ''}
                    onChange={field.onChange}
                    type="text"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )}
    </div>
  );
};

export default Notification;
