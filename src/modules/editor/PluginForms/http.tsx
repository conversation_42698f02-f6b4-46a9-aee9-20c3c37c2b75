import React from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import { FormField, FormItem, FormControl, FormMessage, FormLabel } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';

const requestTypeOptions = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
];

const HTTP = () => {
  const { t } = useTranslation();
  const { control } = useFormContext();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'process.headers',
  });

  const addHeader = () => {
    append({ headerKey: '', headerValue: '' });
  };

  return (
    <div className="flex flex-col gap-4 px-4 py-4">
      <FormField
        control={control}
        name="process.requestType"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                as={FloatingType.SELECT}
                label={t('http.requestType')}
                options={requestTypeOptions}
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="process.URL"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField label={t('http.url')} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="settings.timeout"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField label={t('http.timeout')} type="number" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="space-y-2">
        <FormLabel>{t('http.headers')}</FormLabel>
        {fields.map((field, index) => (
          <div key={field.id} className="flex items-start gap-2">
            <FormField
              control={control}
              name={`process.headers.${index}.headerKey`}
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input placeholder={t('http.headerKey')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`process.headers.${index}.headerValue`}
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input placeholder={t('http.headerValue')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => remove(index)}
              className="text-error-500 shrink-0 mt-2"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
        <Button type="button" variant="outline" onClick={addHeader} className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          {t('http.addHeader')}
        </Button>
      </div>

      <FormField
        control={control}
        name="process.requestBody"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField as={FloatingType.TEXTAREA} label={t('http.requestBody')} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default HTTP;