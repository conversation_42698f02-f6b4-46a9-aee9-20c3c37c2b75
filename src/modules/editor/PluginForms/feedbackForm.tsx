import { useTranslation } from 'react-i18next';
import { feedbackOptions } from '@/lib/constant';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { FloatingField } from '@/components/ui/floating-label';
import DropdownButtonWithDivider from '@/components/dropdownWithDivider';
import ChannelLangDropdown from './utils/ChannelLangDropdown';

export default function FeedbackNode() {
  const { t } = useTranslation();
  const { control, watch } = useFormContext();
  const language = watch('settings.language');
  const platform = watch('settings.platform');
  const prefixKey = `process.channelData.${platform}.${language}.feedbackConfig`;

  return (
    <>
      <ChannelLangDropdown />

      <div className="px-4 pt-4 space-y-4">
        <FormField
          control={control}
          name={`${prefixKey}.feedbackPrompt`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  label={t('Enter the prompt what chatbot asks')}
                  value={field.value}
                  onChange={field.onChange}
                  type="text"
                  className="h-11"
                />
              </FormControl>
              <FormMessage className="text-error-500 text-xs" />
            </FormItem>
          )}
        />
      </div>

      <div className="px-4 pt-4 space-y-4">
        <span className="text-sm text-tertiary-500 px-1">{t('Feedback Type')}</span>
        <FormField
          control={control}
          name={`${prefixKey}.feedbackType`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DropdownButtonWithDivider
                  value={field.value}
                  onChange={field.onChange}
                  options={feedbackOptions}
                  className="min-w-24"
                  placeholder="Select Feedback Type"
                />
              </FormControl>
              <FormMessage className="text-error-500 text-xs" />
            </FormItem>
          )}
        />
      </div>
    </>
  );
}
