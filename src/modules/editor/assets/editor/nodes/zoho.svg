<svg id="Zoho_Corporation-Logo.wine" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="29.678" height="10.235" viewBox="0 0 29.678 10.235">
  <defs>
    <linearGradient id="linear-gradient" x1="-3.462" y1="1.008" x2="-3.456" y2="1.008" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe500"/>
      <stop offset="1" stop-color="#fcb822"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-2.105" y1="1.008" x2="-2.099" y2="1.008" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#168ccc"/>
      <stop offset="1" stop-color="#00649e"/>
    </linearGradient>
    <clipPath id="clip-path">
      <path id="Path_2210" data-name="Path 2210" d="M75.282-80.822l5.6,2.529-2.529,5.741-5.6-2.529,2.529-5.742" transform="translate(-72.753 80.822)"/>
    </clipPath>
    <linearGradient id="linear-gradient-3" y1="1" x2="0.001" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#25a149"/>
      <stop offset="1" stop-color="#008a52"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="1.01" x2="0.006" y2="1.01" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d92231"/>
      <stop offset="1" stop-color="#ba2234"/>
    </linearGradient>
  </defs>
  <path id="Path_2203" data-name="Path 2203" d="M281.48,21.355v7.039l-.972.948V22.459l.972-1.1" transform="translate(-251.803 -19.17)" fill="#e79224"/>
  <path id="Path_2204" data-name="Path 2204" d="M224.2-67.882h-6.436v6.892H224.2Z" transform="translate(-195.481 71.171)" fill="url(#linear-gradient)"/>
  <path id="Path_2205" data-name="Path 2205" d="M218.773,21.362l-1.008,1.1H224.2l.956-1.1-6.384,0" transform="translate(-195.481 -19.171)" fill="#fef26f"/>
  <path id="Path_2206" data-name="Path 2206" d="M148,16.632l6.331-.88-.53,1.518-6,.951.026-1.085.171-.5" transform="translate(-132.675 -14.14)" fill="#91c9ed"/>
  <path id="Path_2207" data-name="Path 2207" d="M151.949-69.441l.877,6.174-6.243.877-.843-5.969.365-.285,5.844-.8" transform="translate(-130.826 72.57)" fill="url(#linear-gradient-2)"/>
  <path id="Path_2208" data-name="Path 2208" d="M206.942,15.75l0,.037.821,6.322-.479,1.333-.877-6.174" transform="translate(-185.29 -14.138)" fill="#0b9ad6"/>
  <g id="Group_9506" data-name="Group 9506" transform="translate(7.445 1.965)" clip-path="url(#clip-path)">
    <path id="Path_2209" data-name="Path 2209" d="M0-100.025H29.677v10.233H0Z" transform="translate(-7.445 98.059)" fill="url(#linear-gradient-3)"/>
  </g>
  <path id="Path_2211" data-name="Path 2211" d="M91.78,0l.582,1.965,5.6,2.529L97.5,2.616,91.78,0" transform="translate(-82.388)" fill="#98d0a0"/>
  <path id="Path_2212" data-name="Path 2212" d="M71.991,0,69.735,5.354l.309,2.353,2.529-5.741L71.991,0" transform="translate(-62.599)" fill="#68bf6b"/>
  <path id="Path_2213" data-name="Path 2213" d="M6.264-69.527l.924,6.076L.994-62.48,0-68.567l6.265-.959" transform="translate(0.001 72.648)" fill="url(#linear-gradient-4)"/>
  <path id="Path_2214" data-name="Path 2214" d="M0,12.048.436,9.879l6.254-1-.424,2.212L0,12.048" transform="translate(-0.001 -7.968)" fill="#ef463d"/>
  <path id="Path_2215" data-name="Path 2215" d="M61.649,8.876l.917,6.134-.417,2.154-.924-6.076.424-2.212" transform="translate(-54.96 -7.968)" fill="#761116"/>
  <path id="Path_2216" data-name="Path 2216" d="M33.261,33.934a.7.7,0,0,0-.2-.434.449.449,0,0,0-.311-.115.663.663,0,0,0-.1.007.484.484,0,0,0-.362.222.6.6,0,0,0-.082.322,1.029,1.029,0,0,0,.012.15l.228,1.607-1.795.264-.228-1.607a.714.714,0,0,0-.2-.43.437.437,0,0,0-.309-.12.583.583,0,0,0-.086.007.508.508,0,0,0-.376.22.585.585,0,0,0-.084.323,1.081,1.081,0,0,0,.012.154L30,38.682a.675.675,0,0,0,.206.433.475.475,0,0,0,.318.111.726.726,0,0,0,.105-.008A.457.457,0,0,0,30.975,39a.613.613,0,0,0,.077-.315,1.053,1.053,0,0,0-.012-.152l-.251-1.651,1.795-.264.251,1.651a.682.682,0,0,0,.2.43.463.463,0,0,0,.315.114.662.662,0,0,0,.1-.007.477.477,0,0,0,.359-.22.6.6,0,0,0,.079-.317,1.067,1.067,0,0,0-.012-.153Zm-7.3,2.311a2,2,0,0,1-.835,1.028,1.17,1.17,0,0,1-.57.151,1.346,1.346,0,0,1-.527-.115,1.209,1.209,0,0,1-.742-.815,1.491,1.491,0,0,1-.053-.4,2.363,2.363,0,0,1,.211-.916,2.043,2.043,0,0,1,.843-1.047,1.166,1.166,0,0,1,.571-.152,1.365,1.365,0,0,1,.534.117,1.2,1.2,0,0,1,.736.816,1.5,1.5,0,0,1,.051.393A2.43,2.43,0,0,1,25.961,36.245Zm.614-2.5a2.357,2.357,0,0,0-.8-.548,2.4,2.4,0,0,0-.944-.2h-.015a2.35,2.35,0,0,0-.954.215h0a2.589,2.589,0,0,0-.858.6,3.24,3.24,0,0,0-.843,1.993,2.719,2.719,0,0,0,.159,1.043,2.3,2.3,0,0,0,.5.823,2.426,2.426,0,0,0,.818.552h0a2.383,2.383,0,0,0,.94.2h.008a2.423,2.423,0,0,0,.952-.205h0a2.721,2.721,0,0,0,.864-.607,3.193,3.193,0,0,0,.844-1.991,2.685,2.685,0,0,0-.167-1.038,2.4,2.4,0,0,0-.509-.835ZM18.8,37.85a.435.435,0,0,0-.195-.129.912.912,0,0,0-.3-.043,2.268,2.268,0,0,0-.379.037l-1.635.279a1.538,1.538,0,0,1,.19-.493,6.746,6.746,0,0,1,.545-.814h0l.189-.252c.031-.039.075-.1.131-.171a3.988,3.988,0,0,0,.694-1.2,2.518,2.518,0,0,0,.093-.537q0-.074,0-.147a2.264,2.264,0,0,0-.032-.382,1.041,1.041,0,0,0-.085-.285.4.4,0,0,0-.145-.17.549.549,0,0,0-.283-.058,2.226,2.226,0,0,0-.365.036l-1.889.323a.966.966,0,0,0-.506.208.4.4,0,0,0-.129.3.574.574,0,0,0,.009.1.423.423,0,0,0,.245.338.7.7,0,0,0,.288.053,1.521,1.521,0,0,0,.252-.024l1.581-.267c0,.028,0,.056,0,.084a.848.848,0,0,1-.051.29,2.284,2.284,0,0,1-.351.556q-.08.1-.243.3a7.549,7.549,0,0,0-.917,1.323h0a2.857,2.857,0,0,0-.272.745,2.056,2.056,0,0,0-.044.411,1.61,1.61,0,0,0,.023.273,1.105,1.105,0,0,0,.095.313.41.41,0,0,0,.162.18.6.6,0,0,0,.277.047,4.043,4.043,0,0,0,.641-.069l1.714-.293a1.415,1.415,0,0,0,.637-.225.4.4,0,0,0,.148-.321.6.6,0,0,0-.009-.1.443.443,0,0,0-.095-.215Zm-1.453-1.586h0Zm22.269,1.4a1.234,1.234,0,0,1-1.007.446,1.246,1.246,0,0,1-1.013-.444,1.862,1.862,0,0,1-.372-1.228,1.908,1.908,0,0,1,.371-1.247,1.365,1.365,0,0,1,2.022,0,1.905,1.905,0,0,1,.371,1.247A1.865,1.865,0,0,1,39.615,37.661Zm1.323-2.271a2.575,2.575,0,0,0-.565-.86,2.426,2.426,0,0,0-.8-.55,2.477,2.477,0,0,0-.961-.185,2.518,2.518,0,0,0-.969.185,2.378,2.378,0,0,0-.8.551,2.489,2.489,0,0,0-.561.856,2.864,2.864,0,0,0-.187,1.047,2.832,2.832,0,0,0,.189,1.043,2.6,2.6,0,0,0,.559.867,2.325,2.325,0,0,0,.794.542,2.56,2.56,0,0,0,.978.183,2.506,2.506,0,0,0,.959-.183,2.44,2.44,0,0,0,.805-.541,2.609,2.609,0,0,0,.565-.866,2.949,2.949,0,0,0,0-2.089" transform="translate(-13.2 -29.621)" fill="#fff"/>
</svg>
