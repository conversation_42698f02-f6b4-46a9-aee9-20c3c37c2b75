import { useCallback, useState } from 'react';
import { debounce } from 'lodash';
import type { UndoRedoState } from '../types';

export const useUndoRedo = (debouncePeriod = 500) => {
  const [index, setIndex] = useState(0);
  const [state, setRawState] = useState<UndoRedoState | null>(null);
  const [states, setStates] = useState<UndoRedoState[]>([]);

  const debouncedSetStates = useCallback(
    debounce((value: UndoRedoState) => {
      setStates(prevStates => {
        const copy = [...prevStates];

        if (copy.length === 1 && copy[0]) {
          copy[0].nodeId = value.nodeId;
          copy[0].type = value.type;
        }

        copy.length = index + 1; // Delete all history after index
        copy.push(value);

        setIndex(copy.length - 1);
        return copy;
      });
    }, debouncePeriod),
    [index, debouncePeriod]
  );

  const updateInitialState = useCallback((init: UndoRedoState) => {
    const copy = JSON.parse(JSON.stringify(init));
    setRawState(copy);
    setStates([copy]);
    setIndex(0);
  }, []);

  const removeLastUndoState = useCallback(() => {
    setIndex(prev => Math.max(0, prev - 1));
    setStates(prev => {
      const copy = [...prev];
      copy.pop();
      return copy;
    });
  }, []);

  const updateCurrentDetails = useCallback(
    (value: UndoRedoState) => {
      const copy = JSON.parse(JSON.stringify(value));
      debouncedSetStates(copy);
    },
    [debouncedSetStates]
  );

  const resetState = useCallback((init: UndoRedoState) => {
    setIndex(0);
    setRawState(init);
    setStates([init]);
  }, []);

  const goBack = useCallback(
    (steps = 1): UndoRedoState | null => {
      const newIndex = Math.max(0, index - steps);
      setIndex(newIndex);

      const newState = states[newIndex];
      if (newState) {
        setRawState(newState);
        return JSON.parse(JSON.stringify(newState));
      }
      return null;
    },
    [index, states]
  );

  const goForward = useCallback(
    (steps = 1): UndoRedoState | null => {
      const newIndex = Math.min(states.length - 1, index + steps);
      setIndex(newIndex);

      const newState = states[newIndex];
      if (newState) {
        setRawState(newState);
        return JSON.parse(JSON.stringify(newState));
      }
      return null;
    },
    [index, states]
  );

  return {
    currentDetails: state,
    updateCurrentDetails,
    resetState,
    currentIndex: index,
    lastIndex: states.length - 1,
    goBack,
    goForward,
    updateInitialState,
    removeLastUndoState,
  };
};
