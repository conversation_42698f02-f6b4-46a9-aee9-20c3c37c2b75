import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ui } from 'rappid';
import { StencilProps, StencilState, Position } from '@/modules/editor/types';
import { getModuleIcon, getModuleText, portsIn, portsOut } from '@/modules/editor/utils/config';
import { getStencilByType } from '@/modules/editor/joint-components/stencil';
import { Rnd } from 'react-rnd';
import { alignStencil, canDrag } from '@/modules/editor/utils/stencilUtil';
import { StencilTabs } from '@/modules/editor/utils/constants';

const useStencil = ({ paper, graph, isMaxNodeReached }: StencilProps) => {
  const [state, setState] = useState<StencilState>({
    collapseStatus: false,
    minimizeStatus: false,
    floatPosition: 'right',
    collapseCache: null,
    position: { x: 0, y: 0 },
    currentTab: StencilTabs.ENGAGE, // Replace with nodePanelTabs[0].key
    search: '',
    isExpandNodeModalOpen: false,
  });

  const [stencilInstance, setStencilInstance] = useState<ui.Stencil | null>(null);
  const dragRef = useRef<Rnd>(null);

  // Initialize position
  useEffect(() => {
    const canvasElement = document.getElementsByClassName('canvas')[0];
    if (canvasElement) {
      const { height, width } = canvasElement.getBoundingClientRect();
      setState(prev => ({
        ...prev,
        position: { y: -height + 15, x: width - 300 },
      }));
    }
  }, []);

  // Initialize stencil
  useEffect(() => {
    if (!paper) return;

    try {
      const instance = new ui.Stencil({
        paper,
        canDrag: cellView => canDrag(cellView, graph),
      });
      setStencilInstance(instance);
    } catch (error) {
      console.error('Failed to initialize stencil', error);
    }
  }, [paper, graph]);

  // Set up stencil event listeners
  useEffect(() => {
    if (!stencilInstance) return;

    const handleDragEnd = (cloneView: any) => {
      if (isMaxNodeReached()) {
        cloneView.el.style.display = 'block';
        stencilInstance.cancelDrag({ dropAnimation: true });
        return;
      }
    };

    stencilInstance.on('element:dragend', handleDragEnd);
    return () => {
      stencilInstance.off('element:dragend', handleDragEnd);
    };
  }, [stencilInstance, graph, isMaxNodeReached]);

  // Fullscreen event listener
  useEffect(() => {
    const handleFullscreenChange = () => {
      alignStencil(setState, state.position, !!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, [state.position]);

  const onControlledDragStop = useCallback((e: any, position: Position) => {
    setState(prev => ({ ...prev, position }));
  }, []);

  const startDrag = useCallback(
    (event: React.PointerEvent, type: string) => {
      if (!type || !stencilInstance) return;

      try {
        const nodeInstance = getStencilByType(type);
        const label = getModuleText(type);

        const cell = new nodeInstance({
          z: 10,
          attrs: {
            ...(type !== 'choiceOption'
              ? { image: { 'xlink:href': getModuleIcon(type), width: 35, height: 35, x: 7, y: 7 } }
              : {}),
            text: { text: label },
          },
          ...(type !== 'appEnd'
            ? { ports: { groups: { in: portsIn, out: portsOut } } }
            : { ports: { groups: { in: portsIn } } }),
        });

        if (canDrag({ model: { attributes: { type } } }, graph)) {
          stencilInstance.startDragging(cell, event.nativeEvent);
        }
      } catch (error) {
        console.error('Failed to start dragging node', error);
      }
    },
    [stencilInstance]
  );

  const handleCollapse = useCallback(() => {
    setState(prev => {
      const newCollapseStatus = !prev.collapseStatus;
      const el = document.getElementsByClassName('react-draggable')[0] as HTMLElement;
      if (el) {
        el.style.transition = 'all 0.2s';
        setTimeout(() => (el.style.transition = 'none'), 200);
      }

      let newPosition: Position = prev.position;
      let newCollapseCache: Position | null = prev.collapseCache ?? null;

      if (prev.floatPosition === 'right') {
        if (!newCollapseStatus) {
          newPosition =
            prev.collapseCache !== null && prev.collapseCache !== undefined
              ? prev.collapseCache
              : prev.position;
          newCollapseCache = null;
        } else {
          newCollapseCache = prev.position;
          newPosition = { ...prev.position, x: prev.position.x + 190 };
        }
      }

      dragRef.current?.updateSize(
        newCollapseStatus ? { width: '80px', height: '100%' } : { width: '275px', height: '100%' }
      );

      return {
        ...prev,
        collapseStatus: newCollapseStatus,
        position: newPosition,
        collapseCache: newCollapseCache,
      };
    });
  }, []);

  const handleMinimize = useCallback(() => {
    setState(prev => {
      const newMinimizeStatus = !prev.minimizeStatus;
      dragRef.current?.updateSize(
        newMinimizeStatus ? { width: '160px', height: '40px' } : { width: '275px', height: '100%' }
      );

      if (!newMinimizeStatus) {
        alignStencil(setState, prev.position, false);
      }

      return { ...prev, minimizeStatus: newMinimizeStatus };
    });
  }, []);

  const handleSearch = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setState(prev => ({ ...prev, search: event.target.value }));
  }, []);

  const handleTabChange = useCallback((tab: string) => {
    setState(prev => ({ ...prev, currentTab: tab }));
  }, []);

  const getStencilDimensions = useMemo(() => {
    if (state.minimizeStatus) return { width: '160px', height: '40px' };
    if (state.collapseStatus) return { width: '80px', height: '100%' };
    return { width: '275px', height: '100%' };
  }, [state.minimizeStatus, state.collapseStatus]);

  return {
    state: { ...state, onControlledDragStop },
    dragRef,
    stencilInstance,
    getStencilDimensions,
    handleCollapse,
    handleMinimize,
    handleSearch,
    handleTabChange,
    startDrag,
  };
};

export default useStencil;
