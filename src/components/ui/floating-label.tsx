import React, { useState, useId } from 'react';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { MultiSelect } from '../multi-select';
import { T } from 'vitest/dist/chunks/environment.d.cL3nLXbE.js';

export type Option = { id?: string; value: string; label: string };

export enum FloatingType {
  INPUT = 'input',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTI_SELECT = 'multi-select',
}

type FloatingFieldCommon = {
  label: string;
  options?: Option[];
  disabled?: boolean;
  searchable?: boolean;
  error?: string;
  type?: string;
  maxLength?: number;
  disableUnselect?: boolean;
  [key: string]: unknown;
};
type FloatingFieldProps =
  | (FloatingFieldCommon & {
      as?: FloatingType.INPUT | FloatingType.TEXTAREA | FloatingType.SELECT;
      value: string;
      onChange: (e: string) => void;
    })
  | (FloatingFieldCommon & {
      as: FloatingType.MULTI_SELECT;
      value: Option[];
      onChange: (e: Option[]) => void;
    });

export function FloatingField({
  label,
  value,
  onChange,
  as = FloatingType.INPUT,
  options = [],
  error,
  disabled,
  type = 'text',
  maxLength,
  disableUnselect,
  ...props
}: FloatingFieldProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [isMultiSelectOpen, setIsMultiSelectOpen] = useState(false);
  const id = useId();

  const hasValue =
    as === 'multi-select'
      ? Array.isArray(value) && value.length > 0
      : (value ?? '').toString().length > 0;

  const isLabelFloating = isFocused || hasValue || (as === 'multi-select' && isMultiSelectOpen);

  const baseClass =
    'w-full border border-tertiary-300 rounded px-3 pt-3 pb-2 text-base text-tertiary-900 focus:outline-none disabled:opacity-50 bg-background focus:ring-1 focus:ring-blue-700 min-h-12';

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (maxLength && e.target.value.length > maxLength) return;
    onChange(e.target.value as any); //TODO make sure change any to it's correct type
  };

  const fieldRenderers: Record<FloatingType, () => React.ReactNode> = {
    textarea: () => (
      <textarea
        aria-label={label}
        {...props}
        id={id}
        value={typeof value === 'string' ? value : ''}
        className={baseClass}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onChange={handleInputChange}
        rows={3}
      />
    ),
    select: () => (
      <Select
        value={value as string}
        onValueChange={val => onChange?.(val as any)}
        disabled={disabled}
      >
        <SelectTrigger id={id} aria-label={label} className={baseClass}>
          <SelectValue placeholder=" " />
        </SelectTrigger>
        <SelectContent className="border-tertiary-300 bg-background">
          {options.map(opt => (
            <SelectItem key={opt.value} value={opt.value}>
              {opt.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    ),
    'multi-select': () => (
      <MultiSelect
        aria-label={label}
        className="border-tertiary-300 bg-background"
        options={options.map(opt => ({
          id: opt.id ?? opt.value,
          value: opt.value,
          label: opt.label,
        }))}
        value={(value as Option[]).map(opt => ({
          id: opt.id ?? opt.value,
          value: opt.value,
          label: opt.label,
        }))}
        onChange={onChange as (value: any) => void}
        disabled={disabled}
        placeholder=" "
        onMenuOpen={() => setIsMultiSelectOpen(true)}
        onMenuClose={() => setIsMultiSelectOpen(false)}
      />
    ),
    input: () => (
      <input
        {...props}
        id={id}
        value={typeof value === 'string' || typeof value === 'number' ? value : ''}
        type={type}
        className={baseClass}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onChange={handleInputChange}
        aria-label={label}
      />
    ),
  };

  const fieldContent = fieldRenderers[as]?.() ?? fieldRenderers.input();

  const labelClasses = cn('absolute left-3 px-1 transition-all duration-200 pointer-events-none', {
    '-top-2 text-xs bg-background text-tertiary-400': isLabelFloating,
    'top-3.5 text-sm pr-5 text-tertiary-500': !isLabelFloating,
  });

  const currentLength = typeof value === 'string' ? value.length : 0;

  return (
    <div className="relative w-full">
      {fieldContent}
      <label htmlFor={id} className={labelClasses}>
        {label}
      </label>
      {maxLength && (as === 'input' || as === 'textarea') && (
        <div className="text-xs text-tertiary-500 mt-1 text-right">
          {currentLength}/{maxLength}
        </div>
      )}
    </div>
  );
}
