'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { DayPicker, DateRange } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { DatePickerProps } from '../DatePicker/types/datepicker.types';
import { DateType } from './types/date-picker.enums';
import { useTranslation } from 'react-i18next';

const getDisabledDate = (fieldType: DateType, customRange?: DateRange) => {
  switch (fieldType) {
    case DateType.FUTURE_DATE:
      return (date: Date) => date < new Date();
    case DateType.PAST_DATE:
      return (date: Date) => date > new Date();
    case DateType.CUSTOM_DATE:
      return customRange
        ? (date: Date) =>
            date < (customRange.from || new Date()) || date > (customRange.to || new Date())
        : () => false;
    default:
      return () => false;
  }
};

export function DatePicker({
  selected,
  onSelect,
  fieldType = DateType.DATE,
  customRange,
  className,
}: DatePickerProps) {
  const [range, setRange] = useState<DateRange>();
  const { t } = useTranslation();

  const handleRangeSelect = (newRange: DateRange | undefined) => {
    setRange(newRange);
    onSelect(newRange);
  };

  const buildButtonLabel = (): string => {
    if (fieldType === DateType.CUSTOM_DATE) {
      if (range?.from && range?.to) {
        return `${format(range.from, 'dd-MM-yyyy')} → ${format(range.to, 'dd-MM-yyyy')}`;
      }
      return t('common.dateRange');
    }

    if (selected) {
      return format(selected, 'dd-MM-yyyy');
    }

    return t('common.date');
  };

  const buttonClasses = cn(
    'w-full justify-start text-left font-normal',
    !selected && fieldType !== DateType.CUSTOM_DATE && 'text-muted-foreground',
    className
  );

  const disabledFn = getDisabledDate(fieldType, customRange);

  return (
    <div className="[[data-radix-popper-content-wrapper]:has(&)]:z-50">
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className={buttonClasses}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            {buildButtonLabel()}
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-full p-0 font-light" side="bottom" align="start" forceMount>
          {fieldType === DateType.CUSTOM_DATE ? (
            <DayPicker
              mode="range"
              selected={range}
              onSelect={handleRangeSelect}
              disabled={disabledFn}
              numberOfMonths={1}
              className="p-2 rounded-md border border-tertiary-200 bg-background"
              modifiersClassNames={{
                selected: 'bg-primary-400 text-white font-semibold rounded-md',
                range_start: 'bg-primary-400 text-white rounded-l-md',
                range_end: 'bg-primary-400 text-white rounded-r-md',
                range_middle: 'bg-primary-100 text-primary-900',
                today: 'border border-primary-400 text-primary-900 font-bold',
              }}
              styles={{
                day: { width: '2.25rem', height: '2.25rem' },
              }}
            />
          ) : (
            <Calendar
              mode="single"
              selected={selected}
              onSelect={onSelect}
              disabled={getDisabledDate(fieldType, customRange)}
              className="w-full p-2 rounded-md border border-tertiary-200 bg-background"
              classNames={{
                day: 'h-9 w-9 flex items-center justify-center rounded-md transition-colors hover:bg-primary-50',
                selected: 'bg-primary-400 text-white font-semibold rounded-md',
                day_today: 'border border-primary-400 text-primary-900 font-bold',
                head_cell: 'text-xs font-semibold text-secondary-500',
                caption: 'py-2 text-base font-medium text-secondary-900',
              }}
            />
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
