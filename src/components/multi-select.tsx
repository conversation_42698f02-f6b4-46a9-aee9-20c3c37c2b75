import React from 'react';
import Select, { components, MultiValue, MultiValueRemoveProps } from 'react-select';
import { cn } from '@/lib/utils';
import '@/styles/multiselectStyle.css';
import { X } from 'lucide-react';
import { Option } from './ui/floating-label';

interface MultiSelectProps {
  options: Option[];
  value: Option[];
  onChange: (value: Option[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  onMenuOpen?: () => void;
  onMenuClose?: () => void;
}

const CustomMultiValueRemove = (props: MultiValueRemoveProps<Option>) => (
  <components.MultiValueRemove {...props}>
    <div
      onClick={e => {
        e.stopPropagation();
        props.innerProps.onClick?.(e);
      }}
    >
      <X className="h-3 w-3 cursor-pointer" />
    </div>
  </components.MultiValueRemove>
);

export function MultiSelect({
  options,
  value,
  onChange,
  placeholder,
  disabled,
  className,
  onMenuOpen,
  onMenuClose,
}: MultiSelectProps) {
  const handleChange = (selectedOptions: MultiValue<Option>) => {
    onChange(selectedOptions as Option[]);
  };

  return (
    <Select
      isMulti
      options={options}
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      isDisabled={disabled}
      className={cn('w-full text-base', className)}
      classNamePrefix="multi-select"
      components={{
        MultiValueRemove: CustomMultiValueRemove,
        IndicatorSeparator: () => null,
      }}
      onMenuOpen={onMenuOpen}
      onMenuClose={onMenuClose}
    />
  );
}
