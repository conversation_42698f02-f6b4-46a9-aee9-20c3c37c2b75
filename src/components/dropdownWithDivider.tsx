import React from 'react';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from './ui/select';
import { cn } from '@/lib/utils';
import { DropdownOption } from '@/types';
import { useTranslation } from 'react-i18next';

export interface DropdownButtonProps<T> {
  value: T;
  onChange: (value: T) => void;
  options: DropdownOption[];
  icon?: React.ReactNode; // TODO: consider removing later
  className?: string;
  triggerClassName?: string;
  placeholder?: string;
  disabled?: boolean;
}

const DropdownButtonWithDivider = <T extends string = string>({
  value,
  onChange,
  options,
  icon,
  className,
  triggerClassName,
  placeholder,
  disabled,
}: DropdownButtonProps<T>) => {
  const { t } = useTranslation();
  const defaultPlaceholder = placeholder || t('common.selectOption');

  return (
    <div className={cn('relative', className)}>
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger
          className={cn(
            'px-4 py-1.5 border border-tertiary-300 rounded-md text-base text-tertiary-500 min-w-24 shadow-none focus:ring-0 focus:ring-offset-0 focus:outline-none gap-1 h-11',
            triggerClassName
          )}
        >
          <SelectValue placeholder={defaultPlaceholder} />
          {icon}
        </SelectTrigger>
        <SelectContent>
          {options?.map(option => (
            <SelectItem key={option.value} value={option.value} className="flex gap-1">
              <div className="flex gap-3 items-center">
              
              {/* Label section */}
              <div className="text-tertiary-500 text-sm min-w-20">{option.label}</div>

              {/* Divider */}
              <div className="w-px h-5 bg-tertiary-300"></div>

              {/* Icon section */}
              <div className='ml-3'>{option.icon}</div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default DropdownButtonWithDivider;
