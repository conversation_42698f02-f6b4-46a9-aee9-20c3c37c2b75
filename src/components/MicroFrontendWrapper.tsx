import React, { forwardRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import '../styles/globals.css';
import '@/lib/i18n/config';
import { cn } from '@/lib/utils';

interface MicroFrontendWrapperProps {
  children: React.ReactNode;
  className?: string;
  fullscreen?: boolean;
}

const MicroFrontendWrapper = forwardRef<HTMLDivElement, MicroFrontendWrapperProps>(
  ({ children, className, fullscreen = true }, ref) => {
    const { i18n } = useTranslation();
    useEffect(() => {
      const languageCode = localStorage.getItem('i18nextLng') || 'en';

      i18n.changeLanguage(languageCode);
    }, []);

    return (
      <div ref={ref} className={cn('mfe-app', fullscreen && 'h-full w-full', className)}>
        {children}
      </div>
    );
  }
);

export default MicroFrontendWrapper;
