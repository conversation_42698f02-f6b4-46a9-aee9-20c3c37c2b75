import React, { useEffect, useMemo, useState } from 'react';
import { useGetBotLanguagesQuery, useGetLanguagesQuery } from '@/store/api';
import DropdownButton from './dropdownButton';
import { Language } from '@/types';

import EnFlag from '@/assets/flags/en.svg';
import DeFlag from '@/assets/flags/de.svg';
import FrFlag from '@/assets/flags/fr.svg';
import EsFlag from '@/assets/flags/es.svg';
import ItFlag from '@/assets/flags/it.svg';
import PtFlag from '@/assets/flags/pt.svg';
import RuFlag from '@/assets/flags/ru.svg';
import ZhCNFlag from '@/assets/flags/zh-CN.svg';
import JaFlag from '@/assets/flags/ja.svg';
import HiFlag from '@/assets/flags/hi.svg';
import { useBotIdParam } from '@/hooks/useRouterParam';

interface IProps {
  onChange: (langId: string, languageNode: Language) => void;
  initialValue?: string;
}

const flagMap: Record<string, string> = {
  en: EnFlag,
  de: DeFlag,
  fr: FrFlag,
  es: EsFlag,
  it: ItFlag,
  pt: PtFlag,
  ru: RuFlag,
  'zh-CN': ZhCNFlag,
  ja: JaFlag,
  hi: HiFlag,
};

const LanguageDropdown: React.FC<IProps> = ({ onChange, initialValue }) => {
  const { botId } = useBotIdParam();
  const [selectedLanguage, setSelectedLanguage] = useState<Language>();

  const { data: botLanguagesData } = useGetBotLanguagesQuery({ filter: { botId: { eq: botId } } });
  const botLanguages = botLanguagesData?.data?.items || [];

  const { data: languagesData } = useGetLanguagesQuery(
    {
      filter: {
        id: {
          in: botLanguages.map(botLanguage => botLanguage.langId),
        },
      },
    },
    {
      skip: !botLanguages.length,
    }
  );

  const botDefaultLanguage = useMemo(() => {
    return botLanguages.find(botLanguage => botLanguage.isDefault);
  }, [botLanguages]);

  const languageList = languagesData?.data?.items || [];

  const languages = useMemo(
    () =>
      languageList.map(language => {
        const FlagIcon = flagMap[language?.code];
        return {
          value: language?.id ?? '',
          label: language?.name ?? '',
          icon: FlagIcon ? (
            <img src={FlagIcon} className="w-5 h-5" data-testid="country-flag" />
          ) : null,
        };
      }),
    [languageList]
  );

  useEffect(() => {
    if (languageList?.length && !selectedLanguage) {
      const languageNode =
        //TODO: rm this code check
        languageList.find(
          language =>
            language.id === initialValue ||
            language.id === botDefaultLanguage?.langId ||
            language.code === 'en'
        ) ?? languageList[0];
      setSelectedLanguage(languageNode);
      onChange?.(languageNode.id, languageNode);
    }
  }, [languageList, selectedLanguage]);

  const onChangeHandler = (langId: string) => {
    const languageNode = languageList.find(language => language.id === langId);
    setSelectedLanguage(languageNode);
    onChange(langId, languageNode!);
  };

  return (
    <DropdownButton
      options={languages ?? []}
      value={selectedLanguage?.id ?? ''}
      onChange={onChangeHandler}
    />
  );
};

export default LanguageDropdown;
