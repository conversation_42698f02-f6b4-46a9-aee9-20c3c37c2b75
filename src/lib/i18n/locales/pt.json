{"common": {"search": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrar", "create": "CRIAR", "save": "SALVAR", "submit": "Enviar", "cancel": "CANCELAR", "delete": "Excluir", "add": "ADICIONAR", "clone": "Clonar", "export": "Exportar", "edit": "<PERSON><PERSON>", "yes": "SIM", "no": "NÃO", "selectOption": "Selecione uma opção", "getStarted": "COMEÇAR", "preview": "Visualizar", "publish": "PUBLICAR", "duplicate": "Duplicar", "versionHistory": "Histórico de Versões", "flows": "Fluxos", "debugger": "Depurador", "message": "Mensagem", "image": "Imagem", "file": "Arquivo", "video": "Vídeo", "addViaUrl": "Ad<PERSON><PERSON>r via URL", "enterFileUrl": "Insira a URL do arquivo", "maxSize": "<PERSON><PERSON><PERSON> máximo: {{size}}MB", "clickOrDrag": "Clique ou arraste o arquivo {{type}} aqui", "clickOrDragFiles": "Clique ou arraste o arquivo para esta área para enviar", "writeMessage": "Escrever mensagem", "typeMessage": "Digite sua mensagem...", "fillAboveField": "Preencha o formulário acima para continuar", "dateRange": "Escolha um intervalo de datas", "trackOrder": "Acompanhar meu pedido", "cancelOrder": "<PERSON><PERSON>ar meu pedido", "chatWithAgent": "Converse com um Agente", "viewSimilarProducts": "Ver produtos se<PERSON>", "hello": "<PERSON><PERSON><PERSON>, {{name}}!", "howCanIHelp": "Como posso ajudá‑lo hoje?", "searchFlows": "Pesquisar fluxos...", "onboarding": "Integração inicial", "notFound": "Não encontrado", "enterValidValue": "Por favor insira um valor válido", "translateTo": "Traduzir para", "translate": "TRADUZIR", "nothingToShow": "<PERSON>da a mostrar", "generate": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "nodeId": "ID do nó:", "noData": "Sem dados", "searchEllipsis": "Pesquisar...", "justNow": "agora mesmo", "update": "<PERSON><PERSON><PERSON><PERSON>", "error": "Erro", "somethingWrong": "<PERSON>go deu errado", "saveChanges": "SALVAR ALTERAÇÕES", "date": "Data", "loading": "Carregando...", "build": "Construir", "import": "Importar", "share": "Compartilhar", "goBack": "Voltar"}, "chatbot": {"botPublishedSuccessfully": "Bot publicado com sucesso!", "untitled": "Chatbot sem título", "noDomain": "PADRÃO", "noDescription": "Sem descrição", "confirmDelete": "CONFIRMAR EXCLUSÃO", "deleteMessage": "Tem certeza que deseja excluir este chatbot?", "chatbotDeleted": "Chatbot excluído com sucesso", "noCancel": "NÃO, CANCELAR", "yesDelete": "SIM, EXCLUIR", "newChatbotPrefix": "Novo‑Chatbot", "cloneFailed": "Falha ao clonar o chatbot.", "buildSuccess": "Bot construído com sucesso!", "buildFailed": "Falha na construção do bot.", "buildStreamError": "Erro ao conectar ao fluxo de construção.", "defaultTitle": "<PERSON><PERSON>", "defaultDomain": "E‑com", "defaultDescription": "Ajuda clientes a navegar pelo processo de compra digital."}, "debugger": {"logs": "Registros", "aiAnalysis": "Análise de IA", "sessionData": "Dados da Sessão", "aiAnalysisContent": "Conte<PERSON><PERSON> da Análise de IA em breve.", "sessionDataContent": "Conteúdo dos Dados da Sessão em breve.", "noAiAnalysisLogs": "Nenhum registro de Análise de IA disponível.", "noSessionDataLogs": "Nenhum registro de Dados da Sessão disponível.", "noLogs": "Nenhum registro disponível."}, "preview": {"confirmDialog": "Deseja encerrar esta conversa?", "confirmDialogDesc": "<PERSON><PERSON> a<PERSON> o chat e fechará a janela"}, "validation": {"maxLength": "Este campo não pode exceder {{count}} caracteres.", "BLANK_URL": "A URL não pode estar vazia.", "URL_TOO_LONG": "A URL é muito longa.", "INVALID_URL": "Formato de URL inválido.", "URL_DISPLAY_TEXT_TOO_LONG": "O texto de exibição não pode exceder 100 caracteres.", "invalidPhoneNumber": "Número de telefone inválido para {{field}}.", "fieldRequired": "{{field}} <PERSON> obrigat<PERSON>.", "invalidEmail": "<PERSON>ail inv<PERSON>lido para {{field}}.", "passwordMinLength": "A senha para {{field}} deve ter no mínimo 6 caracteres.", "invalidTimeFormat": "Formato de hora inválido para {{field}}.", "invalidDate": "Data inválida para {{field}}.", "pastDateRequired": "{{field}} deve ser uma data passada (antes de {{date}})", "futureDateRequired": "{{field}} deve ser uma data futura (após {{date}})", "invalidNumber": "Número in<PERSON> para {{field}}"}, "home": {"title": "NeuraTalk AI", "description": "é uma solução de IA conversacional de ponta projetada para melhorar o engajamento do cliente, automatizar o suporte e agilizar operações de negócios.", "noResults": "<PERSON><PERSON><PERSON> chatbot encontrado correspondente à sua busca.", "lastUpdated": "Última atualização em {{date}}"}, "editor": {"chatbotName": "Nome do Chatbot", "domain": "<PERSON><PERSON><PERSON>", "description": "Descrição", "uploadImage": "Clique ou arraste um arquivo para esta área para enviar", "uploadFormat": "(Tamanho: Até 5MB | Formato: jpg, png)", "unsupportedFile": "Tipo de arquivo não suportado", "fileTooLarge": "O arquivo deve ter menos de 2MB", "invalidName": "<PERSON><PERSON> let<PERSON>, n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (‑), sub<PERSON><PERSON><PERSON> (_), e pontos (.) são permitidos", "invalidImageFile": "<PERSON>r <PERSON>, envie um arquivo de imagem válido (png, jpg, jpeg, webp, gif, svg)", "nameRequired": "O nome do chatbot é obrigatório", "nameMaxError": "O nome do chatbot não pode exceder 50 caracteres", "domainRequired": "O domínio é obrigatório", "descMaxError": "A descrição não pode exceder 150 caracteres", "updateSuccess": "Bot atualizado com sucesso", "descRequired": "A descrição é obrigatória", "updateError": "Falha ao atualizar o bot", "writeMessage": "Escrever Mensagem"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "<PERSON><PERSON><PERSON>"}, "domains": {"ecomm": "E‑com", "telecom": "Telecom", "retail": "Varejo", "travel": "Viagem", "other": "Outro"}, "emptyState": {"title": "Nada aqui ainda", "description": "Atualmente não há conteúdo para exibir."}, "intents": {"title": "Intenções", "addTitle": "ADICIONAR INTENÇÃO", "editTitle": "EDITAR INTENÇÃO", "name": "Nome da Intenção", "namePlaceholder": "Nome da intenção", "nameLabel": "Nome da intenção", "nameRequired": "O nome da intenção é obrigatório.", "startAdding": "Comece a adicionar intenções", "noFlowsConnected": "Nenhum fluxo conectado", "selectToManage": "Selecione uma intenção para gerenciar as expressões", "loading": "Carregando intenções.", "loadingError": "Erro ao carregar intenções.", "intentAdded": "Intenção adicionada com sucesso.", "intentUpdated": "Intenção atualizada com sucesso.", "intentDeleted": "Intenção excluída com sucesso.", "confirmDeleteTitle": "CONFIRMAR EXCLUSÃO DA INTENÇÃO", "deleteConfirmationMessage": "Tem certeza que deseja excluir esta intenção?", "utterances": {"title": "Expressões", "addTitle": "ADICIONAR EXPRESSÃO", "editTitle": "EDITAR EXPRESSÃO", "enterPlaceholder": "Digite a expressão", "startAdding": "Comece a adicionar expressões", "emptyError": "A expressão não pode estar vazia.", "loading": "Carregando expressões.", "loadingError": "Erro ao carregar expressões.", "utteranceAdded": "Expressão adicionada.", "utteranceUpdated": "Expressão atualizada.", "utteranceDeleted": "Expressão excluída.", "confirmDeleteTitle": "CONFIRMAR EXCLUSÃO DA EXPRESSÃO", "deleteConfirmationMessage": "Tem certeza que deseja excluir esta expressão?"}}, "entities": {"title": "Entidades", "addTitle": "ADICIONAR ENTIDADE", "entityName": "Nome da Entidade", "entityNamePlaceholder": "Nome da entidade", "type": "Tipo", "selectType": "Selecionar tipo", "enablePartialMatch": "Ativar correspondência parcial", "startAdding": "Comece a adicionar entidades", "loading": "Carregando entidades...", "error": "Erro ao carregar entidades.", "noEntitiesFound": "Nenhuma entidade encontrada", "searchEntities": "Pesquisar entidades...", "selected": "Selecionado", "removeEntity": "Remover entidade", "types": {"text": "Texto", "list": "Lista", "regex": "REGEX"}, "table": {"name": "Nome", "type": "Tipo", "value": "Valor", "action": "Ação"}, "validation": {"nameRequired": "O nome da entidade é obrigatório.", "typeRequired": "O tipo da entidade é obrigatório.", "valueRequired": "O valor é obrigatório."}, "addValue": "Adicionar valor", "editTitle": "EDITAR ENTIDADE", "regexValuePlaceholder": "Valor Regex", "entityAdded": "Entidade adicionada com sucesso.", "entityUpdated": "Entidade atualizada com sucesso.", "entityDeleted": "Entidade excluída com sucesso.", "confirmDeleteTitle": "CONFIRMAR EXCLUSÃO DA ENTIDADE", "deleteConfirmationMessage": "Tem certeza que deseja excluir esta entidade?"}, "train": {"entities": {"title": "Entidades", "content": "Conteúdo das Entidades", "addTitle": "ADICIONAR ENTIDADE", "nameLabel": "Nome da entidade", "intentIdLabel": "ID da intenção", "metadataLabel": "Metadados (JSON)", "metadataPlaceholder": "Insira metadados como JSON", "loading": "Carregando entidades...", "error": "Erro ao carregar entidades.", "validation": {"nameRequired": "O nome da entidade é obrigatório.", "intentIdRequired": "O ID da intenção é obrigatório.", "invalidJson": "Formato JSON inválido para os metadados."}}, "synonyms": {"title": "Sinônimos", "content": "Conte<PERSON><PERSON> de Sinônimos"}, "smallTalk": {"title": "Conversa informal", "content": "Conteúdo de conversa informal"}, "trainFromLogs": {"title": "Treinar a partir dos registros", "content": "Conteúdo de Treinar a partir dos registros"}, "tabs": {"intentUtterances": "Expressões da Intenção", "entities": "Entidades", "faqs": "Perguntas Frequentes", "synonyms": "Sinônimos", "smallTalk": "Conversa informal", "trainFromLogs": "Treinar a partir dos registros"}}, "faqs": {"title": "Perguntas & Respostas", "category": {"title": "Categoria", "addTitle": "ADICIONAR CATEGORIA", "editTitle": "EDITAR CATEGORIA", "nameLabel": "Nome da categoria", "nameRequired": "O nome da categoria é obrigatório.", "startAdding": "Comece a adicionar categorias", "selectToManage": "Selecione uma categoria para gerenciar perguntas", "categoryAdded": "Categoria adicionada com sucesso.", "categoryUpdated": "Categoria atualizada com sucesso.", "categoryDeleted": "Categoria excluída com sucesso.", "confirmDeleteTitle": "CONFIRMAR EXCLUSÃO DA CATEGORIA", "deleteConfirmationMessage": "Tem certeza que deseja excluir esta categoria?"}, "loading": "Carregando FAQs.", "loadingError": "Erro ao carregar FAQs.", "items": {"loading": "Carregando itens de FAQ...", "loadingError": "Erro ao carregar itens de FAQ.", "startAdding": "Comece a adicionar perguntas", "addTitle": "ADICIONAR PERGUNTA", "editTitle": "EDITAR PERGUNTA", "questionLabel": "<PERSON><PERSON><PERSON>", "questionPlaceholder": "Digite a pergunta", "questionEmpty": "A pergunta não pode estar vazia.", "atLeastOne": "Pelo menos uma pergunta é obrigatória.", "answerLabel": "Resposta", "answerPlaceholder": "Digite a resposta", "answerEmpty": "A resposta não pode estar vazia.", "linkFlowLabel": "Vincular Fluxo", "chooseFlowPlaceholder": "Escolha o Fluxo", "primaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "questionPrefix": "P", "answerPrefix": "R", "questionsAdded": "Perguntas adicionadas.", "questionsUpdated": "Perguntas atualizadas.", "maxQuestions": "Você pode adicionar no máximo {{count}} pergun<PERSON>.", "questionsDeleted": "Perguntas excluídas.", "confirmDeleteTitle": "CONFIRMAR EXCLUSÃO DA FAQ", "deleteConfirmationMessage": "Tem certeza que deseja excluir este item de FAQ?"}, "validation": {"questionRequired": "A pergunta é obrigatória.", "atLeastOneQuestion": "Pelo menos uma pergunta é obrigatória.", "answerRequired": "A resposta é obrigatória."}}, "fileUpload": {"fileTooLarge": "O arquivo deve ter menos de {{size}}MB e ser do tipo {{type}}", "someFilesRejected": "Alguns arquivos foram rejeitados. Certifique-se de tipos corretos e tamanho < {{size}}MB.", "failedToUpload": "Falha ao enviar: {{filename}}"}, "tabs": {"contentComingSoon": "<PERSON><PERSON><PERSON><PERSON> {{tabName}} em breve"}, "builder": {"tabs": {"design": "Design", "train": "T<PERSON>inar", "channels": "Canais", "agentTransfer": "Transferência de Agente", "integrations": "Integrações", "settings": "Configurações"}}, "flows": {"untitledFlow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "welcome": "Bem‑vindo", "fallback": "Substituto", "targetFlow": "Fluxo Destino", "existingFlow": "Lembrar contexto entre o fluxo de conexão", "errorLoading": "Erro ao carregar fluxos", "newFlow": "Novo Fluxo", "fetchError": "Falha ao buscar fluxos"}, "agentTransfer": {"transfer": "Integre o Agente a partir da página ‘Transferir Agente’ para configurar o Agente Nativo", "selectAgentTransfer": "Selecione uma Transferência de Agente", "nothingSelected": "<PERSON>da se<PERSON>", "filters": {"all": "Todos", "native": "Nativo", "thirdParty": "Te<PERSON><PERSON><PERSON>"}, "tabs": {"available": "Disponível", "myAgentTransfers": "Minhas Transferências de Agente"}, "setupHeading": "Configurar", "setupDescription": "Forneça os detalhes abaixo para ativar o suporte de {{agentTransferName}} para o chatbot.", "generateToken": "GERAR TOKEN", "liveAgentPortalDetails": "Detalhes do Portal Live Agent", "pendingStatus": "Pendente", "remove": "REMOVER", "chatbotNameLabel": "Nome do chatbot:", "accessTokenLabel": "<PERSON><PERSON> Acesso:", "shareInstruction": "Compartilhe o acima com o Administrador do Agente para ativação."}, "settings": {"language": "Idioma", "nlu": "NLU", "personalization": "Personalização", "llmConfiguration": "Configuração LLM", "cannedResponses": "Respostas <PERSON>nta<PERSON>", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor", "languages": "Idiomas", "yourLanguages": "<PERSON><PERSON>", "noLanguagesSelected": "Nenhum idioma selecionado", "availableLanguages": "Idiomas Disponíveis", "searchLanguages": "Pesquisar idiomas...", "defaultTag": "Padrão", "allSelectedLanguages": "Todos os idiomas selecionados", "languagesSaved": "Idiomas salvos com sucesso"}, "stencil": {"nodes": "<PERSON>ós", "searchNodes": "<PERSON><PERSON><PERSON><PERSON>...", "engage": "<PERSON><PERSON><PERSON>", "utilities": "Utilitários", "marketplace": "Marketplace"}, "platform": {"web": "Web", "mobile": "Mobile"}, "pagination": {"previous": "Anterior", "next": "Próximo", "morePages": "<PERSON><PERSON>", "loadingMore": "Carregando mais...", "noItemsFound": "Nenhum item encontrado", "errorLoadingData": "Erro ao carregar dados", "tryAgain": "Tentar novamente", "defaultFilterUI": "UI de filtro padrão – personalizável com prop children"}, "form": {"loadingForm": "Carregando formulário...", "typing": "Digitando...", "enterLabel": "Inserir etiqueta", "prompt": "Prompt", "textField": "Campo de Texto", "label": "Etiqueta"}, "errors": {"failedToSend": "Falha ao enviar mensagem", "unexpectedResponse": "Resposta inesperada do servidor", "somethingWrong": "<PERSON>go deu errado"}, "channels": {"selectWABA": "Selecione um número WABA para conectar", "changeNumber": "MUDAR NÚMERO", "webhook": "Webhook", "webhookInstruction": "Cole este Webhook contra o número WABA no canal NGAGE WhatsApp para integrar.", "switchToMeta": "Trocar para Meta Cloud API", "switchDescription": "Troque para Meta Cloud API e vincule seu Chatbot via BSP parceiro.", "switch": "TROCAR", "connect": "CONECTAR", "selectChannels": "Selecione os canais para configurar", "nothingSelected": "<PERSON>da se<PERSON>", "myChannels": "<PERSON><PERSON>", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voz", "alexa": "Alexa", "available": "Disponível", "invalid": "INVÁLIDO", "testChannel": "Canal de Teste", "getStarted": "COMEÇAR", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "sms": "SMS", "virtualReceptionist": "Recepcionista Virtual", "email": "Email", "rcs": "RCS", "chatbot": "<PERSON><PERSON><PERSON>", "network": "Rede", "studio": "Estúdio", "allChannels": "Todos os Canais", "filters": {"all": "Todos", "native": "Nativo", "text": "Texto", "voice": "Voz"}, "tabs": {"available": "Disponível", "myChannels": "<PERSON><PERSON>"}}, "nodes": {"agentTransfer": "Transferência de Agente", "appEnd": "Fim do App", "appStart": "Início do App", "choice": "Escolha", "choiceOption": "Opção de Escolha", "feedback": "<PERSON><PERSON><PERSON>", "flowConnector": "<PERSON><PERSON> de Fluxo", "http": "HTTP", "interactiveMessage": "Mensagem Interativa", "language": "Idioma", "message": "Mensagem", "notification": "Notificação", "payment": "Pagamento", "script": "<PERSON><PERSON><PERSON>", "text": "Texto", "waitDelay": "Aguardar atraso", "whatsapp": "WhatsApp"}, "bots": {"testBot": "<PERSON><PERSON>", "testChatbot": "<PERSON><PERSON><PERSON>", "aChatbot": "<PERSON> chatbot de teste", "aChatbotDescription": "Descrição de um chatbot de teste", "myFlow": "<PERSON><PERSON>", "lastUpdatedToday": "Última atualização hoje"}, "whatsapp": {"onboarding": {"ngage": {"description": "Integre o WABA usando o canal NGAGE WhatsApp e integre com seu Chatbot."}, "meta": {"description": "Integre o WABA usando a Meta Cloud API e vincule seu Chatbot via o BSP parceiro."}}}, "loading": {"hangTight": "Aguarde! Estamos configurando seu espaço de trabalho...", "almostThere": "Quase lá..."}, "timePicker": {"hour": "<PERSON><PERSON>", "min": "Min", "amPm": "AM/PM"}, "richTextEditor": {"bold": "Negrito", "italic": "Itálico", "underline": "<PERSON><PERSON><PERSON><PERSON>", "strikeThrough": "<PERSON><PERSON><PERSON><PERSON>", "highlight": "Realce", "superscriptSubscript": "Sobrescrito/Índice", "emoji": "<PERSON><PERSON><PERSON>"}, "notification": {"notificationChannel": "Selecione o Canal de Notificação", "configureMessage": "Configurar Men<PERSON>", "configureSMS": "Configurar SMS", "selectSenderID": "Selecione o ID do Remetente", "recipientMSISDN": "Digite o MSISDN do Destinatário", "configureEmail": "<PERSON><PERSON><PERSON><PERSON>", "selectEmail": "Selecione o Endereço de Email", "recipientEmail": "Digite o Endereço de Email do Destinatário", "enterSubjectOfEmail": "Digite o Assunto do Email"}}