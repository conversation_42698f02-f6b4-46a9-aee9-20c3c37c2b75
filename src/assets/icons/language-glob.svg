<svg id="Group_24452" data-name="Group 24452" xmlns="http://www.w3.org/2000/svg" width="105.077" height="90.366" viewBox="0 0 105.077 90.366">
  <path id="Path_5210" data-name="Path 5210" d="M711.165,295.538a30.517,30.517,0,1,1-1.932-10.746c.067.179.131.358.195.539v0q.423,1.193.744,2.431A30.727,30.727,0,0,1,711.165,295.538Z" transform="translate(-623.31 -242.235)" fill="#3f3d56"/>
  <path id="Path_5211" data-name="Path 5211" d="M717.534,298.22a10.308,10.308,0,0,0-9.05-.37,4.881,4.881,0,0,0-3.02,3.322c1.707-1.008,3.917.584,4.356,3.138l-.1.028a6.245,6.245,0,0,0,1.289,4.791,3.37,3.37,0,0,1,1.617-2.4,6.409,6.409,0,0,0,2.114-1.669,10.691,10.691,0,0,0,1.1-3.745C716.1,300,716.607,298.586,717.534,298.22Z" transform="translate(-663.764 -265.644)" fill="#6c63ff"/>
  <path id="Path_5212" data-name="Path 5212" d="M670.675,311.283a10.882,10.882,0,0,1-1.671,1.71,3.846,3.846,0,0,0-1.215,2.272c-.072.618.039,1.246.024,1.871s-.224,1.328-.649,1.483c-.671.242-1.183-1-1.872-1.091-.548-.068-1.041.794-.882,1.541s.9,1.079,1.333.591c.387.65.114,1.83-.468,2.029-.062.41-.123.818-.183,1.227a20.187,20.187,0,0,1-4.209-3.583,10.875,10.875,0,0,1-2.478-5.858c-.162-1.732.079-3.714-.717-5.047a2.737,2.737,0,0,0-2.712-1.236c-.179.012-.36.032-.539.055a30.385,30.385,0,0,1,2.2-5.035,2.157,2.157,0,0,0,.551-.343c.915-.777,1.321-2.349,2.063-3.436q.072-.1.149-.2a7.075,7.075,0,0,1,2.76-1.977c2.176-1.057,4.416-2.127,6.713-1.98l-.654.063q-1.144,3.205-2.291,6.411-.174.492-.352.985a10.133,10.133,0,0,0-.814,3.869,2.361,2.361,0,0,0,.852,1.942c.5.256,1.039-.058,1.519-.369l.608-.391c.729-.469,1.536-.951,2.306-.637a2.721,2.721,0,0,1,1.325,2.277A4.552,4.552,0,0,1,670.675,311.283Z" transform="translate(-626.461 -263.656)" fill="#496fdb"/>
  <path id="Path_5213" data-name="Path 5213" d="M709.044,400.851l-6.386-3.932.036-.093c-1.323.5-1.873,2.857-1.654,4.78s.96,3.644,1.335,5.518a14.647,14.647,0,0,1-.552,7.686,62.943,62.943,0,0,0,7.394-10.189,5.453,5.453,0,0,0,.669-1.483,2.359,2.359,0,0,0-.156-1.678A1.869,1.869,0,0,0,709.044,400.851Z" transform="translate(-660.495 -338.634)" fill="#496fdb"/>
  <path id="Path_5214" data-name="Path 5214" d="M699.093,318.615a2.353,2.353,0,0,0-.108,1.712,1.9,1.9,0,0,0,1.06.982l1.176.583a1.518,1.518,0,0,0,1.212.23,1.616,1.616,0,0,0,.728-1.647,2.931,2.931,0,0,0-1-1.694l-1.824-1.832-.343.42A4.575,4.575,0,0,0,699.093,318.615Z" transform="translate(-658.945 -280.243)" fill="#6c63ff"/>
  <path id="Path_5215" data-name="Path 5215" d="M780.528,316.846l1.587.374a4.843,4.843,0,0,1-2.166,4.04,4.041,4.041,0,0,0-1.456.807,4.428,4.428,0,0,0-.731,1.712l-.906,3.3c-.324,1.185-.741,2.5-1.584,2.927a1.752,1.752,0,0,1-2.108-.835,10.043,10.043,0,0,1-1.225-2.751,2.832,2.832,0,0,1-1.188,2.5,1.552,1.552,0,0,1-2.048-.8c-.341-.6-.483-1.389-.818-2s-1.036-.956-1.4-.386a5.187,5.187,0,0,0-.329,1.441,1.564,1.564,0,0,1-1.307,1.258,2.18,2.18,0,0,1-1.565-.715,7.09,7.09,0,0,1-1.944-3.921,4.678,4.678,0,0,1,.055-2.144c.21-.664.717-1.176,1.22-1.05.331-.7-.323-1.628-.911-1.546s-1.053.715-1.538,1.2-1.159.84-1.645.357a.667.667,0,0,1-.625.707h-.052a2.421,2.421,0,0,1-.882-.468,2.394,2.394,0,0,0-3.322,1.08c.495-2.106,1.22-4.523,2.741-5.018.621-.2,1.3-.036,1.886-.387,1.039-.623,1.249-2.977.352-3.948a3.786,3.786,0,0,1-.393,2.618.831.831,0,0,1-1.441-.71.622.622,0,0,1-1.157.246,2.236,2.236,0,0,1-.067-1.883,4.434,4.434,0,0,1,.995-1.413,12.207,12.207,0,0,1,2.572-2.294,2.916,2.916,0,0,1,2.968-.188c.5.287.934.765,1.441,1.017a1.068,1.068,0,0,0,1.47-.344h0l.017-.028a8.1,8.1,0,0,0,.34-2.473c.251-1.463,1.714-2.147,2.525-1.181-.606-.155-1.232.562-1.307,1.43a2.063,2.063,0,0,0,.949,2.009c.987.437,1.953-.662,2.7-1.68q1.167-1.6,2.338-3.193a3.862,3.862,0,0,1,.418-.509,1.346,1.346,0,0,1,1.838-.038c.788.643,1.159,1.963,1.806,2.871a5.271,5.271,0,0,0,3.651,1.9q1.462.284,2.926.572a30.316,30.316,0,0,1,1.7,3.75A10.277,10.277,0,0,0,780.528,316.846Z" transform="translate(-698.978 -270.508)" fill="#496fdb"/>
  <path id="Path_5216" data-name="Path 5216" d="M846.194,424.417a30.6,30.6,0,0,1-2.543,4.255c-.614-.263-1.275.207-1.85.647-.6.461-1.323.91-1.939.49a2.7,2.7,0,0,1-.633-2.588,6.893,6.893,0,0,1,1.181-2.431,4.765,4.765,0,0,1,1.772-1.855,1.381,1.381,0,0,1,1.838,1.137l.505-.017c-.283-.389-.135-1.195.246-1.336.439-.166.819.437,1.079.969C845.965,423.928,846.083,424.171,846.194,424.417Z" transform="translate(-761.493 -357.542)" fill="#6c63ff"/>
  <path id="Path_5217" data-name="Path 5217" d="M758.565,379.691c-1.086.068-1.946-1.269-2.518-2.584s-1.093-2.819-2.073-3.489a6.057,6.057,0,0,0-1.805-.62,2.236,2.236,0,0,1-1.533-1.35l-.689-.278a10.346,10.346,0,0,0-2.615,1.865,5.845,5.845,0,0,0-1.558,3.407c-.132,1.379.354,2.916,1.262,3.434.717.409,1.553.151,2.279.526.908.468,1.447,1.828,1.651,3.171s.152,2.738.3,4.1a5.4,5.4,0,0,0,1.381,3.467c1.042.871,2.571.147,3.038-1.438.229-.778.221-1.655.393-2.462a13.029,13.029,0,0,1,1.763-3.642A5.5,5.5,0,0,0,758.565,379.691Z" transform="translate(-693.214 -320.026)" fill="#496fdb"/>
  <path id="Path_5218" data-name="Path 5218" d="M753.049,343.962a2.9,2.9,0,0,1,.283.3.986.986,0,0,1-.017,1.018.948.948,0,0,0,.679-.345,4.365,4.365,0,0,0,.52-.733.8.8,0,0,0,.153-.639c-.051-.146-.173-.209-.258-.319a2.167,2.167,0,0,1-.238-.691,1.247,1.247,0,0,0-.9-.952l.054.01a.516.516,0,0,0-.242.55c.033.213.17.39.156.607-.02.312-.332.459-.351.771A.6.6,0,0,0,753.049,343.962Z" transform="translate(-698.429 -298.264)" fill="#6c63ff"/>
  <path id="Path_5219" data-name="Path 5219" d="M746.577,330.372h0a.883.883,0,0,0,.093-.1Z" transform="translate(-693.817 -289.983)" fill="#6c63ff"/>
  <path id="Path_5220" data-name="Path 5220" d="M743.126,332.077a.947.947,0,0,0,.295-.939c-.079-.355-.358-.594-.616-.528a.627.627,0,0,1-.583.172c-.159-.041-.334-.147-.472-.027a.715.715,0,0,0-.161.314l-.153.471a.3.3,0,0,0-.018.178.151.151,0,0,0,.116.092l.92.271A.792.792,0,0,0,743.126,332.077Z" transform="translate(-690.041 -290.221)" fill="#6c63ff"/>
  <path id="Path_5221" data-name="Path 5221" d="M829.259,407.232l-.425-.7a2.325,2.325,0,0,0-.56-.7.5.5,0,0,0-.7.062h0l-.007.009.017.133a2.994,2.994,0,0,1,.747,1.457c.032.237.021.483.049.721a.841.841,0,0,0,.263.588c.223.149.485-.071.644-.339a1.167,1.167,0,0,0,.181-.743,1.3,1.3,0,0,0-.209-.488Z" transform="translate(-753.022 -345.134)" fill="#6c63ff"/>
  <path id="Path_5222" data-name="Path 5222" d="M838.987,405.223a1.605,1.605,0,0,0-.052-.648,1.422,1.422,0,0,1,.216-.806,2.624,2.624,0,0,0,.337-.739.619.619,0,0,0-.016-.362c-.047-.105-.161-.153-.226-.07l.14.09a1.271,1.271,0,0,0-.566.027.7.7,0,0,0-.429.484,2.992,2.992,0,0,1-.113.5c-.083.171-.244.228-.346.378a.9.9,0,0,0,.014.859,1,1,0,0,0,.567.446C838.685,405.443,838.909,405.447,838.987,405.223Z" transform="translate(-760.526 -342.822)" fill="#6c63ff"/>
  <path id="Path_5223" data-name="Path 5223" d="M853.13,413.184a1.949,1.949,0,0,0,.245.45c.19.191.447.074.677.027a1.171,1.171,0,0,1,1.114.447,2.835,2.835,0,0,0-1.631-2.035,4.792,4.792,0,0,0-1.677-.262l-.1.025C852.01,412.559,852.789,412.542,853.13,413.184Z" transform="translate(-770.709 -349.588)" fill="#6c63ff"/>
  <path id="Path_5224" data-name="Path 5224" d="M794.492,422.271v0l.028-.055Z" transform="translate(-728.844 -357.198)" fill="#6c63ff"/>
  <path id="Path_5225" data-name="Path 5225" d="M790.629,423.582a5.341,5.341,0,0,0-.337.74,6.448,6.448,0,0,0-.244,1.267c-.031.244-.04.556.113.675a.217.217,0,0,0,.284-.039,1.123,1.123,0,0,0,.206-.306,7.044,7.044,0,0,0,.695-1.649,2.869,2.869,0,0,0-.114-1.849Z" transform="translate(-725.582 -357.346)" fill="#496fdb"/>
  <path id="Path_5226" data-name="Path 5226" d="M769.813,304.817a.684.684,0,0,0,.737.144,2.435,2.435,0,0,0,.694-.458,1.632,1.632,0,0,0,.42-.459.93.93,0,0,0,.086-.709.72.72,0,0,0-.549-.434l-1.021-.3.246-.222c-.253-.136-.534.116-.692.429a2.308,2.308,0,0,0-.21.647,1.66,1.66,0,0,0,.288,1.365Z" transform="translate(-710.562 -269.563)" fill="#6c63ff"/>
  <path id="Path_5227" data-name="Path 5227" d="M792.437,300.189a.562.562,0,0,0,.766.062.857.857,0,0,0,.339-.611c.024-.268-.108-.565-.3-.588l.043-.251c-.364-.066-.812-.079-1.008.363A1.038,1.038,0,0,0,792.437,300.189Z" transform="translate(-727.176 -266.952)" fill="#6c63ff"/>
  <ellipse id="Ellipse_643" data-name="Ellipse 643" cx="2.272" cy="3.047" rx="2.272" ry="3.047" transform="translate(38.226 45.177)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_644" data-name="Ellipse 644" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(79.33 86.789) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_645" data-name="Ellipse 645" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(66.043 87.035) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_646" data-name="Ellipse 646" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(81.69 75.735) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_647" data-name="Ellipse 647" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(87.096 70.806) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_648" data-name="Ellipse 648" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(88.088 62.467) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_649" data-name="Ellipse 649" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(74.695 78.499) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_650" data-name="Ellipse 650" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(81.413 80.93) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_651" data-name="Ellipse 651" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(73.257 87.817) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_652" data-name="Ellipse 652" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(13.287 75.631) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_653" data-name="Ellipse 653" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(0 75.877) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_654" data-name="Ellipse 654" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(15.647 64.577) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_655" data-name="Ellipse 655" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(21.054 59.649) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_656" data-name="Ellipse 656" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(22.046 51.309) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_657" data-name="Ellipse 657" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(8.653 67.341) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_658" data-name="Ellipse 658" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(15.37 69.772) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_659" data-name="Ellipse 659" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(7.214 76.659) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_660" data-name="Ellipse 660" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(94.009 24.542) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_661" data-name="Ellipse 661" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(80.723 24.788) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_662" data-name="Ellipse 662" cx="2.033" cy="1.414" rx="2.033" ry="1.414" transform="translate(96.37 13.488) rotate(-14.913)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_663" data-name="Ellipse 663" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(101.777 8.56) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_664" data-name="Ellipse 664" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(102.769 0.22) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_665" data-name="Ellipse 665" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(89.375 16.252) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_666" data-name="Ellipse 666" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(96.093 18.684) rotate(-7.068)" fill="#e6e6e6"/>
  <ellipse id="Ellipse_667" data-name="Ellipse 667" cx="0.894" cy="1.285" rx="0.894" ry="1.285" transform="translate(87.937 25.57) rotate(-7.068)" fill="#e6e6e6"/>
  <g id="Group_24453" data-name="Group 24453" transform="translate(9.466 4.998)">
    <path id="Path_5229" data-name="Path 5229" d="M13.044,17.336a1.288,1.288,0,0,1-.914-.378L4.378,9.206A1.292,1.292,0,0,1,6.206,7.378l7.752,7.752a1.292,1.292,0,0,1-.914,2.206Z" transform="translate(-0.124 0.752)"/>
    <path id="Path_5230" data-name="Path 5230" d="M4.292,18.212a1.292,1.292,0,0,1-.914-2.206L11.04,8.345l2.513-3.769A1.292,1.292,0,0,1,15.7,6.009L13.119,9.885a1.293,1.293,0,0,1-.161.2L5.206,17.833A1.288,1.288,0,0,1,4.292,18.212Z" transform="translate(-0.416 -0.124)"/>
    <path id="Path_5231" data-name="Path 5231" d="M17.8,6.584H2.292A1.292,1.292,0,1,1,2.292,4H17.8a1.292,1.292,0,0,1,0,2.584Z" transform="translate(-1 -0.124)"/>
    <path id="Path_5232" data-name="Path 5232" d="M8.584,3.584H7.292A1.292,1.292,0,1,1,7.292,1H8.584a1.292,1.292,0,1,1,0,2.584Z" transform="translate(0.46 -1)"/>
    <path id="Path_5233" data-name="Path 5233" d="M25.213,26.5a1.292,1.292,0,0,1-1.157-.714l-5.3-10.609-5.3,10.609a1.292,1.292,0,1,1-2.311-1.156l6.46-12.92a1.292,1.292,0,0,1,2.311,0l6.46,12.92a1.292,1.292,0,0,1-1.155,1.87Z" transform="translate(1.92 1.92)"/>
    <path id="Path_5234" data-name="Path 5234" d="M22.044,19.584H14.292a1.292,1.292,0,1,1,0-2.584h7.752a1.292,1.292,0,1,1,0,2.584Z" transform="translate(2.504 3.672)"/>
  </g>
  <path id="Path_5228" data-name="Path 5228" d="M582.6,314.625c-2.174,0-3.587-.54-4.212-1.614-1.234-2.117.832-5.833,6.14-11.043l.446.454c-4.93,4.84-7.074,8.487-6.036,10.269.657,1.127,2.611,1.531,5.654,1.168a57.307,57.307,0,0,0,11.9-3.194c10.033-3.621,22.621-9.729,35.444-17.2s24.346-15.406,32.445-22.348c1.556-1.334,2.968-2.616,4.2-3.813a41.78,41.78,0,0,0,4.45-4.96c1.817-2.467,2.43-4.366,1.773-5.494-1.1-1.883-5.7-1.706-12.975.5l-.185-.609c7.8-2.365,12.413-2.436,13.709-.21.806,1.383.2,3.466-1.811,6.192a42.412,42.412,0,0,1-4.519,5.038c-1.238,1.206-2.66,2.5-4.226,3.84-8.128,6.966-19.684,14.926-32.539,22.414s-25.48,13.614-35.549,17.247a57.9,57.9,0,0,1-12.037,3.227A17.544,17.544,0,0,1,582.6,314.625Z" transform="translate(-570.616 -234.894)" fill="#3f3d56"/>
</svg>
