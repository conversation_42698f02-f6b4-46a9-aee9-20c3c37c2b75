.multi-select__control {
  @apply bg-background rounded-md min-h-[2.5rem] px-2 py-1 shadow-none focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-300;
}

.multi-select__control--is-disabled {
  @apply bg-tertiary-100 opacity-70;
}

.multi-select__multi-value {
  @apply bg-tertiary-200 text-tertiary-700 rounded px-1;
}

.multi-select__multi-value__label {
  @apply text-sm;
}

.multi-select__multi-value__remove {
  @apply text-tertiary-500 hover:bg-tertiary-300 hover:text-tertiary-700 rounded;
}

.multi-select__menu {
  @apply bg-background shadow-lg rounded-md;
}

.multi-select__option {
  @apply text-sm px-2 py-1 cursor-pointer;
}

.multi-select__option--is-focused {
  @apply bg-tertiary-100;
}

.multi-select__option--is-selected {
  @apply bg-blue-500 text-white;
}

.multi-select__placeholder {
  @apply text-tertiary-400 text-sm;
}

.multi-select__dropdown-indicator {
  @apply text-tertiary-400 hover:text-tertiary-600;
}
