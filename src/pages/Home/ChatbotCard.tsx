import * as React from 'react';
import { useState } from 'react';
import { Pencil, MoreVertical, Copy, Download, Trash, AlertCircle } from 'lucide-react';
import chatbot from '@/assets/icons/Group_10840.svg';
import { ChatbotStatus } from '@/types/enums/enums';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { RoutesName } from '@/lib/constant';
import { useTranslation } from 'react-i18next';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';

export interface ChatbotCardProps {
  id: string;
  title: string;
  description?: string;
  status?: ChatbotStatus | string;
  lastUpdated?: string;
  onDelete: (id: string) => void;
  onClone: (id: string) => void;
}

const ChatbotCard: React.FC<ChatbotCardProps> = ({
  id,
  title,
  description,
  status,
  lastUpdated,
  onDelete,
  onClone,
}) => {
  const { t } = useTranslation();
  const isLive = status === ChatbotStatus.Live;
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const navigate = useNavigate();

  const handleClone = () => {
    onClone(id);
    console.log(`Cloning chatbot: ${title}`);
  };

  const handleExport = () => {
    console.log(`Exporting chatbot: ${title}`);
  };

  const handleDeleteClick = () => {
    console.log('delete-button-', id);

    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    onDelete(id);
    setIsDeleteDialogOpen(false);
  };

  const navigateToChatbot = () => {
    navigate(RoutesName.NEURA_TALK_BUILDER.replace(':botId', id));
  };

  return (
    <div
      className="bg-white cursor-pointer rounded-lg border border-tertiary-200 shadow-sm overflow-hidden flex flex-col p-5 hover:shadow-md transition-shadow duration-200"
      onClick={navigateToChatbot}
    >
      <div className="w-full h-40 rounded-md flex items-center justify-center mb-4 bg-indigo-50">
        <div className="relative w-20 h-20 text-indigo-300">
          <img src={chatbot} alt="Chatbot" className="w-full h-full object-cover" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between items-start mb-2">
          <h3
            className="text-base font-semibold text-slate-800 mr-2 flex-1 truncate"
            title={title}
            data-testid="title"
          >
            {title}
          </h3>
          <span
            className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-sm text-xs font-medium whitespace-nowrap',
              isLive
                ? 'bg-success-500 bg-opacity-20 text-success-500'
                : 'bg-primary-400 bg-opacity-20 text-primary-400'
            )}
            data-testid="status"
          >
            <span
              className={`h-2 w-2 rounded-sm mr-1.5 ${isLive ? 'bg-success-500' : 'bg-primary-400'}`}
            ></span>
            {status}
          </span>
        </div>
        <p
          className="text-sm text-slate-500 mb-4 h-10 overflow-hidden text-ellipsis"
          title={description}
          data-testid="description"
        >
          {description}
        </p>
      </div>
      <div className="border-t border-slate-100 pt-3 mt-auto">
        <div className="flex justify-between items-center">
          <p className="text-xs text-slate-400" data-testid="last-updated">
            {t('home.lastUpdated', { date: lastUpdated })}
          </p>
          <div className="flex items-center space-x-1.5">
            <button
              className="text-slate-400 cursor-pointer hover:text-slate-600 p-1 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
              aria-label="Edit Chatbot"
            >
              <Pencil className="h-4 w-4" data-testid="pencil-icon" />
            </button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  className="text-slate-400 cursor-pointer hover:text-slate-600 p-1 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  aria-label="More options"
                  data-testid={`more-options-${id}`}
                >
                  <MoreVertical className="h-4 w-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40" onClick={e => e.stopPropagation()}>
                <DropdownMenuItem onClick={handleClone} data-testid={`clone-button-${id}`}>
                  <Copy className="mr-2 h-4 w-4" />
                  <span>{t('common.clone')}</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExport}>
                  <Download className="mr-2 h-4 w-4" />
                  <span>{t('common.export')}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDeleteClick}
                  className="text-error-600 focus:bg-error-50 focus:text-error-700"
                  data-testid={`delete-button-${id}`}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  <span>{t('common.delete')}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      <DeleteConfirmationModal
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={confirmDelete}
        title={t('chatbot.confirmDelete')}
        description={t('chatbot.deleteMessage')}
      />
    </div>
  );
};

export default ChatbotCard;
