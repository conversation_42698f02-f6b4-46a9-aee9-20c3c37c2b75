import { render, screen, within } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ChatbotCard, { ChatbotCardProps } from '../Home/ChatbotCard';
import { ChatbotStatus } from '@/types/enums/enums';
import { describe, expect, it, vi } from 'vitest';
import { userEvent } from '@testing-library/user-event';

describe('ChatbotCard', () => {
  const defaultProps: ChatbotCardProps = {
    id: '1',
    title: 'Test Bot',
    description: 'A test chatbot',
    status: ChatbotStatus.Live,
    lastUpdated: 'today',
    onDelete: vi.fn(),
    onClone: vi.fn(),
  };

  const renderWrapper = (props?: Partial<ChatbotCardProps>) => {
    render(
      <MemoryRouter>
        <ChatbotCard {...defaultProps} {...props} />
      </MemoryRouter>
    );
  };

  // Helper function to open dropdown menu
  const openDropdown = async () => {
    const moreOptionsButton = screen.getByLabelText('More options');
    await userEvent.click(moreOptionsButton);
  };

  it('renders title, description, and last updated', () => {
    renderWrapper();
    expect(screen.getByText('Test Bot')).toBeInTheDocument();
    expect(screen.getByText('A test chatbot')).toBeInTheDocument();
    expect(screen.getByTestId('last-updated')).toHaveTextContent('Last updated today');
  });

  it('shows live status with correct color', () => {
    renderWrapper({ status: ChatbotStatus.Live });
    const statusElement = screen.getByText(ChatbotStatus.Live);
    expect(statusElement).toBeInTheDocument();
    expect(statusElement).toHaveClass('bg-success-500', 'text-success-500');
  });

  it('shows draft status with correct color', () => {
    renderWrapper({ status: ChatbotStatus.Draft });
    const statusElement = screen.getByText(ChatbotStatus.Draft);
    expect(statusElement).toBeInTheDocument();
    expect(statusElement).toHaveClass('bg-primary-400', 'text-primary-400');
    expect(statusElement.querySelector('.bg-primary-400')).toBeInTheDocument();
  });

  it('renders chatbot image', () => {
    renderWrapper();
    const image = screen.getByAltText('Chatbot');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', expect.stringContaining('Group_10840.svg'));
  });

  it('calls onClone when Clone is clicked', async () => {
    renderWrapper();
    await openDropdown();
    const cloneButton = screen.getByText('Clone');
    await userEvent.click(cloneButton);
    expect(defaultProps.onClone).toHaveBeenCalledWith('1');
  });

  it('calls onDelete when YES, DELETE is confirmed', async () => {
    renderWrapper();
    await openDropdown();
    const deleteButton = screen.getByText('Delete');
    await userEvent.click(deleteButton);
    expect(screen.getByText('CONFIRM DELETE')).toBeInTheDocument();
    const confirmButton = screen.getByText('YES, DELETE');
    await userEvent.click(confirmButton);
    expect(defaultProps.onDelete).toHaveBeenCalledWith('1');
  });

  it('closes delete dialog when NO, CANCEL is clicked', async () => {
    renderWrapper();
    await openDropdown();
    const deleteButton = screen.getByText('Delete');
    await userEvent.click(deleteButton);
    expect(screen.getByText('CONFIRM DELETE')).toBeInTheDocument();
    const cancelButton = screen.getByText('NO, CANCEL');
    await userEvent.click(cancelButton);
    expect(screen.queryByText('CONFIRM DELETE')).not.toBeInTheDocument();
  });

  it('calls export handler when Export is clicked', async () => {
    const logSpy = vi.spyOn(console, 'log');
    renderWrapper();
    await openDropdown();
    const exportButton = screen.getByText('Export');
    await userEvent.click(exportButton);
    expect(logSpy).toHaveBeenCalledWith('Exporting chatbot: Test Bot');
    logSpy.mockRestore();
  });

  it('renders edit button', () => {
    renderWrapper();
    const editButton = screen.getByLabelText('Edit Chatbot');
    expect(editButton).toBeInTheDocument();
    expect(editButton.querySelector('[data-testid="pencil-icon"]')).toBeInTheDocument();
  });

  it('truncates long title and description with tooltip', () => {
    const longTitle = 'A'.repeat(100);
    const longDescription = 'B'.repeat(200);
    renderWrapper({ title: longTitle, description: longDescription });
    const titleElement = screen.getByTestId('title');
    const descriptionElement = screen.getByTestId('description');
    expect(titleElement).toHaveAttribute('title', longTitle);
    expect(descriptionElement).toHaveAttribute('title', longDescription);
    expect(titleElement).toHaveClass('truncate');
    expect(descriptionElement).toHaveClass('text-ellipsis');
  });

  it('shows alert message in delete dialog', async () => {
    renderWrapper();
    await openDropdown();
    const deleteButton = screen.getByText('Delete');
    await userEvent.click(deleteButton);
    const dialog = screen.getByRole('dialog');
    expect(
      within(dialog).getByText(/Are you sure you want to delete this chatbot?/i)
    ).toBeInTheDocument();
  });

  it('handles empty title and description gracefully', () => {
    renderWrapper({ title: '', description: '' });
    expect(screen.getByTestId('last-updated')).toHaveTextContent('Last updated today');
    expect(screen.getByTestId('title')).toHaveTextContent('');
    expect(screen.getByTestId('description')).toHaveTextContent('');
  });

  it('renders correctly with missing lastUpdated', () => {
    renderWrapper({ lastUpdated: '' });
    expect(screen.getByTestId('last-updated')).toHaveTextContent('Last updated');
  });

  it('does not break with invalid status', () => {
    renderWrapper({ status: 'INVALID' as any });
    const statusElement = screen.getByText('INVALID');
    expect(statusElement).toBeInTheDocument();
    expect(statusElement).toHaveClass('bg-primary-400', 'text-primary-400');
  });

  it('edit button is accessible and focusable', () => {
    renderWrapper();
    const editButton = screen.getByLabelText('Edit Chatbot');
    expect(editButton).toHaveAttribute('aria-label', 'Edit Chatbot');
    expect(editButton).not.toHaveAttribute('disabled');
    editButton.focus();
    expect(editButton).toHaveFocus();
  });

  it('dropdown menu is accessible', async () => {
    renderWrapper();
    const moreOptionsButton = screen.getByLabelText('More options');
    expect(moreOptionsButton).toHaveAttribute('aria-label', 'More options');
    await userEvent.click(moreOptionsButton);
    const dropdown = screen.getByRole('menu');
    expect(dropdown).toBeInTheDocument();
    expect(within(dropdown).getByText('Clone')).toBeInTheDocument();
    expect(within(dropdown).getByText('Export')).toBeInTheDocument();
    expect(within(dropdown).getByText('Delete')).toBeInTheDocument();
  });

  it('delete dialog is accessible', async () => {
    renderWrapper();
    await openDropdown();
    const deleteButton = screen.getByText('Delete');
    await userEvent.click(deleteButton);
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();
    expect(dialog).toHaveAccessibleName('CONFIRM DELETE');
    expect(dialog).toHaveAccessibleDescription(/Are you sure you want to delete this chatbot?/i);
  });
});
