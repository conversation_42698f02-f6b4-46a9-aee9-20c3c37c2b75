import React, { useState } from 'react';
import { <PERSON>Up, TvMinimalPlay, Wrench, Loader2 } from 'lucide-react';
import { neuraTalkBuilderTabs } from './config';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/hooks/useRedux';
import { togglePreview } from '@/store/slices/uiSlice';
import { useTranslation } from 'react-i18next';
import { useBuildBotMutation, usePublishBotMutation } from '@/store/api/chatBotApi';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { getBaseUrl, ApiSliceIdentifier } from '@/store/helper';
import { BuildStatus, BuildStreamData } from '@/types/build.type';

const TabBar = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [buildBot] = useBuildBotMutation();
  const [publishBot, { isLoading: isPublishing }] = usePublishBotMutation();
  const { toast } = useToast();
  const { botId } = useBotIdParam();
  const [isBuilding, setIsBuilding] = useState(false);

  const handlePublishBot = async () => {
    try {
      await publishBot({ botId }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('chatbot.botPublishedSuccessfully')} />,
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: (error as any)?.data?.error?.message || t('common.somethingWrong'),
        variant: 'destructive',
      });
    }
  };

  const handleBuildBot = async () => {
    if (!botId) return;

    setIsBuilding(true);

    let eventSource: EventSource | null = null;

    try {
      // Start SSE connection
      const sseBaseUrl = getBaseUrl(ApiSliceIdentifier.BOT_BUILDER_SERVICE);
      eventSource = new EventSource(`${sseBaseUrl}/bots/${botId}/build/stream`);

      eventSource.onerror = error => {
        console.error('SSE Error:', error);
        toast({
          title: t('common.error'),
          description: t('chatbot.buildStreamError'),
          variant: 'destructive',
        });
        if (eventSource) {
          eventSource.close();
        }
        setIsBuilding(false);
      };

      try {
        // Trigger the build POST endpoint
        await buildBot({ botId }).unwrap();
      } catch (buildError) {
        eventSource?.close();

        setIsBuilding(false);
        toast({
          title: t('common.error'),
          description: (buildError as any)?.data?.error?.message || t('common.somethingWrong'),
          variant: 'destructive',
        });
        return;
      }

      eventSource.onmessage = (event: MessageEvent) => {
        const data: BuildStreamData = JSON.parse(event.data);
        if (data.status === BuildStatus.COMPLETED) {
          toast({
            title: <SuccessToastMessage message={t('chatbot.buildSuccess')} />,
          });
          eventSource?.close();
          setIsBuilding(false);
        } else if (data.status === BuildStatus.FAILED) {
          toast({
            title: t('common.error'),
            description: data.errorMessage || t('chatbot.buildFailed'),
            variant: 'destructive',
          });
          eventSource?.close();
          setIsBuilding(false);
        }
      };
    } catch (error) {
      console.error('Unexpected error during build process:', error);
      eventSource?.close();

      toast({
        title: t('common.error'),
        description: (error as any)?.data?.error?.message || t('common.somethingWrong'),
        variant: 'destructive',
      });
      setIsBuilding(false);
    }
  };

  return (
    <div className="px-6 border-b bg-white">
      <div className="flex items-start justify-between">
        <TabsList className="flex flex-1 p-0 h-full items-stretch">
          {neuraTalkBuilderTabs.map(tab => {
            const Icon = tab.icon;
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  'p-3 flex-1 border border-b-0 rounded-lg rounded-br-none rounded-bl-none bg-primary-50 text-sm transition-colors flex items-center justify-center',
                  'data-[state=active]:text-primary-500 data-[state=active]:bg-primary-100 data-[state=active]:shadow-none',
                  'data-[state=inactive]:text-tertiary-600 data-[state=inactive]:hover:text-secondary-700 data-[state=inactive]:hover:bg-secondary-100',
                  'overflow-hidden'
                )}
              >
                {Icon}
                <span>{t(tab.labelKey)}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <div className="flex items-center space-x-3 flex-[0.5] justify-end">
          <Button variant={'ghost'} className="uppercase" onClick={() => dispatch(togglePreview())}>
            <TvMinimalPlay className="w-4 h-4" />
            <span>{t('common.preview')}</span>
          </Button>
          <Button variant={'outline'} className="uppercase" onClick={handleBuildBot}>
            {isBuilding ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Wrench className="w-4 h-4" />
            )}

            <span>{t('common.build')}</span>
          </Button>
          <Button disabled={isPublishing} onClick={handlePublishBot}>
            {isPublishing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <MonitorUp className="w-4 h-4" />
            )}
            <span>{t('common.publish')}</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TabBar;
