// TODO: Remove this file once flow create api is implemented correctly
export const generateRandomSuffix = () => {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  let suffix = '';
  for (let i = 0; i < 2; i++) {
    suffix += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  for (let i = 0; i < 2; i++) {
    suffix += numbers.charAt(Math.floor(Math.random() * numbers.length));
  }
  return suffix;
};
