import { FilterOptions } from '@/types';

interface DateRange {
  lowerBound: string | null;
  upperBound: string | null;
}

const formatDate = (
  year: number,
  month: number,
  day: number,
  hours = 0,
  minutes = 0,
  seconds = 0,
  ms = 0
): string => new Date(year, month, day, hours, minutes, seconds, ms).toISOString();

export const getDateRange = (type: FilterOptions['dateFilter']): DateRange => {
  const now = new Date();
  const y = now.getFullYear();
  const m = now.getMonth();
  const d = now.getDate();

  const startOfToday = formatDate(y, m, d);
  const endOfToday = formatDate(y, m, d, 23, 59, 59, 999);
  const startOfYesterday = formatDate(y, m, d - 1);
  const endOfYesterday = formatDate(y, m, d - 1, 23, 59, 59, 999);
  const startOf7DaysAgo = formatDate(y, m, d - 7);
  const startOf30DaysAgo = formatDate(y, m, d - 30);
  const startOfMonth = formatDate(y, m, 1);
  const endOfMonth = formatDate(y, m + 1, 0, 23, 59, 59, 999);

  const ranges: Record<FilterOptions['dateFilter'], DateRange> = {
    Today: { lowerBound: startOfToday, upperBound: endOfToday },
    Yesterday: { lowerBound: startOfYesterday, upperBound: endOfYesterday },
    'Last 7 days': { lowerBound: startOf7DaysAgo, upperBound: endOfToday },
    'Last 30 days': { lowerBound: startOf30DaysAgo, upperBound: endOfToday },
    'This Month': { lowerBound: startOfMonth, upperBound: endOfMonth },
    All: { lowerBound: null, upperBound: null },
  };

  return ranges[type];
};

export const getDaysAgo = (dateString: string): string => {
  const updatedDate = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - updatedDate.getTime()) / 1000);

  if (seconds < 30) return 'Just now';

  const minutes = Math.floor(seconds / 60);
  if (minutes < 1) return '1 minute ago';
  if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;

  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours} hour${hours !== 1 ? 's' : ''} ago`;

  const days = Math.floor(hours / 24);
  return `${days} day${days !== 1 ? 's' : ''} ago`;
};
