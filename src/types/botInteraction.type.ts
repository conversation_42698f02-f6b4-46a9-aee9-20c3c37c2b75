export enum ChannelType {
  WEB = 'web',
  WHATSAPP = 'whatsapp',
}

export interface NLUResponseEntity {
  confidence_entity: number;
  end: number;
  entity: string;
  extractor: string;
  processors: string[];
  start: number;
  value: string;
}

export interface NLUResponse {
  intent: {
    name: string;
    confidence: number;
  };
  entities: NLUResponseEntity[];
  text: string;
  conversationId?: string;
}

export interface SendMessageRequest {
  botId: string;
  content: string;
  messageType?: 'text' | 'image' | 'file';
  channelType?: ChannelType;
  formData?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface OutgoingMessage {
  nodeType: string;
  data: {
    text?: string;
    type?: string;
    [key: string]: any;
  };
}

export interface SendMessageResponse {
  conversationId: string;
  response: OutgoingMessage[];
}

export enum DebuggerEventType {
  LOG = 'log',
  NLU_LOG = 'nlu_log',
  CONTEXT = 'context',
}

export enum LogLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
}

export interface LogPayload {
  level: LogLevel;
  message: string;
  details?: any;
}

export interface NluLogPayload {
  intent: {
    name: string;
    confidence: number;
  };
  entities: any[];
  text: string;
}

export interface ConversationContext {
  chatConversationId: string;
  userId?: string;
  botId: string;
  currentJourneyId?: string | null;
  invokedIntent: NLUResponse | null;
  waitingForInput?: boolean;
  asyncOperationInProgress?: boolean;
  sessionStartedAt: Date;
  lastActivityAt: Date;
  metadata: any;
  expiresAt: Date;
  preservedContext?: any;
  journeyContext: any;
}

interface BaseDebuggerEvent {
  timestamp: string;
  conversationId: string;
}

export interface LogEvent extends BaseDebuggerEvent {
  type: DebuggerEventType.LOG;
  payload: LogPayload;
}

export interface NluLogEvent extends BaseDebuggerEvent {
  type: DebuggerEventType.NLU_LOG;
  payload: NluLogPayload;
}

export interface ContextEvent extends BaseDebuggerEvent {
  type: DebuggerEventType.CONTEXT;
  payload: ConversationContext;
}

export type DebuggerEvent = LogEvent | NluLogEvent | ContextEvent;
