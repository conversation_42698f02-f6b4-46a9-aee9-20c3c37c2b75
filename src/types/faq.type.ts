import { PaginationParams } from './pagination.type';
import { Language } from './language.type';

// FAQ Category types

export enum FaqCategoryType {
  DEFAULT = 'DEFAULT',
  CUSTOM = 'CUSTOM',
}
export interface FaqCategory {
  id: string;
  botId: string;
  name: string;
  type: FaqCategoryType;
  description?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

export interface CreateFaqCategoryRequest {
  botId: string;
  name: string;
  description?: string;
}

export interface UpdateFaqCategoryRequest {
  botId?: string;
  name?: string;
  description?: string;
}

// FAQ Item types - included in FaqTranslation response
export interface FaqItem {
  id: string;
  botId: string;
  flowId?: string;
  categoryId: string;
  // Fields from translation for getFaqsByCategoryAndLanguage query
  questions: string[];
  answer: string;
  langId: string;
  faqId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  availableLanguages?: Omit<Language, 'createdAt'>[];
}

// FAQ Translation types
export interface FaqTranslation {
  id: string;
  faqId: string;
  langId: string;
  questions: string[];
  answer: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

export interface CreateFaqTranslationRequest {
  faqId?: string;
  categoryId: string;
  botId: string;
  flowId?: string;
  langId: string;
  questions: string[];
  answer: string;
  metadata?: Record<string, any>;
}

export interface UpdateFaqTranslationRequest {
  questions?: string[];
  answer?: string;
  metadata?: Record<string, any>;
  flowId?: string;
}

export interface FaqIdParam {
  faqId: string;
}

export interface FaqTranslationByLangParam {
  faqId: string;
  langId: string;
}

export interface GetFaqsByCategoryAndLanguageParams extends PaginationParams {
  categoryId: string;
  langId: string;
}

// New types for API responses
export interface CreateFaqResponseData {
  id: string;
  categoryId: string;
  botId: string;
  createdBy: string;
  updatedBy: string;
  updatedAt: string;
  createdAt: string;
  translation: FaqTranslation; // Nested FaqTranslation
}

export interface UpdateFaqResponseData extends FaqTranslation {
  faqItem: FaqItem; // Nested FaqItem
}