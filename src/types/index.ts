export * from './api.type';
export * from './language.type';
export * from './faq.type';
export * from './intent.type';
export * from './entity.type';
export * from './ui.type';
export * from './pagination.type';
export * from './botInteraction.type';
export * from './flow.type';
import { DateFilter, StatusFilter } from '@/types/enums/enums';
import { NavigateFunction } from 'react-router-dom';

// Core Types
export interface UserDetails {
  realm_access: {
    roles: string[];
  };
  scope: string;
}

export interface AppData {
  links: string[];
  startId: string;
  modules: Record<string, ModuleData>;
  errors: Record<string, any>;
  version: string;
  id: string;
}

export interface ModuleData {
  settings: ModuleSettings;
  process: ModuleProcess;
  output: ModuleOutput;
  input: ModuleInput;
  type: string;
  typeId: string;
  coordinates: ModuleCoordinates;
}

export interface ModuleSettings {
  aparty?: string;
  nodeName: string;
  image?: string;
}

export interface ModuleProcess {
  cronjob?: string;
  params?: string[];
  trigger?: string;
  success?: {
    code: any[];
    message: string;
    nodeName: string;
  };
  customErrors?: Array<{
    code: any[];
    message: string;
  }>;
  defaultError?: {
    code: string;
    message: string;
  };
  match_conditions?: Array<{
    id: string;
    statement: Array<{
      expr: [string, string, string];
    }>;
    fallbackcode: string;
    isActive: boolean;
  }>;
}

export interface ModuleOutput {
  conditions?: Record<string, any>;
  fallbackcode?: string;
  codeActive?: boolean;
  customCode?: string;
  customCodeIds?: {
    conditionalLink: any[];
  };
}

export interface ModuleInput {
  [key: string]: any;
}

export interface ModuleCoordinates {
  x: number;
  y: number;
  nodeData: {
    title: string;
    name: string;
    id: string;
    isEditable: boolean;
    canDelete: boolean;
    status: string;
    moduleType: string;
  };
}

export interface Application {
  id: string;
  name: string;
  desc: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  triggers: number;
  channels: number;
  nodes: number;
  svg: string | null;
  ngage_id?: string;
  username?: string;
  nodeExecCharge?: number;
  freeNodeExec?: number;
  OTC?: number;
  MRC?: number;
  appData: AppData;
}

export interface Template {
  id: string;
  name: string;
  desc: string;
  type: string;
  svg: string;
}

export interface DropdownOption {
  value: string;
  label: React.ReactNode;
  icon?: React.ReactNode;
}

export interface ModalTypeDetails {
  id: string;
  type: string;
  top: number;
  left: number;
}

export interface AutoSuggestionPosition {
  top: number;
  left: number;
  element: any;
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  data?: any;
}

export interface ValidationError {
  field: string;
  message: string;
}

// Node Types
export type NodeType =
  | 'appStart'
  | 'appEnd'
  | 'whatsapp'
  | 'voice'
  | 'email'
  | 'sms'
  | 'webhook'
  | 'http'
  | 'choice'
  | 'repeat'
  | 'script'
  | 'addcontact'
  | string;

export interface NodeConfig {
  icon: string;
  text: string;
  description: string;
  tags: string[];
  enabled: boolean;
  type: 'Channels' | 'Utilities' | 'Marketplace';
}

// Redux State Types
export interface RootState {
  applications: ApplicationsState;
  templates: TemplatesState;
  editor: EditorState;
  ui: UIState;
}

export interface ApplicationsState {
  items: Application[];
  loading: boolean;
  error: string | null;
  filters: FilterOptions;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
  };
}

export interface TemplatesState {
  items: Template[];
  loading: boolean;
  error: string | null;
}

export interface EditorState {
  currentApp: Application | null;
  settingDetails: Record<string, ModuleData>;
  modalTypeDetails: ModalTypeDetails | null;
  autoSuggestionPosition: AutoSuggestionPosition | null;
  isSimulatorEnabled: boolean;
  loading: boolean;
  error: string | null;
}

export interface UIState {
  viewType: 'grid' | 'list';
  isMyFlows: boolean;
  showFilterDialog: boolean;
  loader: boolean;
}

export interface Bot {
  id?: string;
  name: string;
  description?: string;
  status?: string;
  domain?: string;
  image?: string;
  lastUpdated?: string;
  createdAt?: string;
}

export interface HeaderProps {
  navigate: NavigateFunction;
}

export interface FormErrors {
  name: string;
  domain: string;
  description: string;
  image: string;
}

export interface DomainOption {
  value: string;
  label: string;
}

export interface TranslationFunction {
  (key: string): string;
}

export interface FilterOptions {
  dateFilter: DateFilter;
  statusFilter: StatusFilter;
  searchTerm: string;
}

export interface HomeProps {
  navigate: NavigateFunction;
}
