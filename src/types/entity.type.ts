import { PaginationParams } from './pagination.type';

export enum EntityType {
  TEXT = 'TEXT',
  REGEX = 'REGEX',
  DATE = 'DATE',
  NUMBER = 'NUMBER',
  EMAIL = 'EMAIL',
}

export interface Entity {
  id: string;
  name: string;
  type: EntityType;
  botId: string;
  intentId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

export interface CreateEntityRequest {
  botId: string;
  intentId?: string;
  name: string;
  type: string;
  metadata?: Record<string, any>;
}

export type UpdateEntityRequest = Partial<Omit<CreateEntityRequest, 'botId'>>;

export interface EntityPaginationParams extends PaginationParams {
  botId: string;
}
