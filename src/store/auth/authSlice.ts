import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';

export interface AuthState {
  isLoggedIn: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
}

export interface AuthResData {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  'not-before-policy': number;
  session_state: string;
  scope: string;
}

const initialState: AuthState = {
  accessToken: sessionStorage.getItem('access') ?? null,
  refreshToken: sessionStorage.getItem('refresh') ?? null,
  expires: null,
  isLoggedIn: !!sessionStorage.getItem('access'),
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const { access_token, refresh_token, expires_in } = action.payload;
      state.accessToken = access_token;
      state.refreshToken = refresh_token;
      state.expires = expires_in;
      state.isLoggedIn = true;
    },
    unsetCredentials: state => {
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
    },
    rehydrateAuthFromSessionStorage: state => {
      state.accessToken = sessionStorage.getItem('access') ?? null;
      state.refreshToken = sessionStorage.getItem('refresh') ?? null;
      state.isLoggedIn = !!sessionStorage.getItem('access');
    },
  },
});

export const { setCredentials, unsetCredentials, rehydrateAuthFromSessionStorage } = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) => state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) => state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) => state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
