import {
  ApiResponse,
  AssignFlowToIntentRequest,
  CreateIntentItemRequest,
  CreateUtteranceTranslationRequest,
  IntentItem,
  IntentUtteranceGetAllParams,
  IntentUtteranceTranslation,
  PaginatedResponse,
  PaginationParams,
  UpdateIntentItemRequest,
  UpdateUtteranceTranslationRequest,
  UtteranceTranslationByLangParam,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';
import { createEntityMixins } from '../../lib/utils/optimizedTags';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['IntentItem', 'IntentUtteranceTranslation'],
});

// IntentItem configuration
const intentItemMixins = createEntityMixins<
  IntentItem,
  'IntentItem',
  typeof updatedApiSlice,
  PaginationParams
>({
  entityType: 'IntentItem',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const {
      filter: { botId },
    } = queryArgs;

    return `intent-items_${JSON.stringify(botId)}`;
  },
});

// IntentUtteranceTranslation configuration
const utteranceMixins = createEntityMixins<
  IntentUtteranceTranslation,
  'IntentUtteranceTranslation',
  typeof updatedApiSlice,
  IntentUtteranceGetAllParams
>({
  entityType: 'IntentUtteranceTranslation',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs, endpointName }) => {
    if (endpointName === 'getTranslationByUtteranceIdAndLangId') {
      const { utteranceId, langId } = queryArgs as UtteranceTranslationByLangParam;
      return `intent-utterance-translation_${utteranceId}_${langId}`;
    }
    const {
      intentId,
      langId,
      query: { page, ...rest } = {},
    } = queryArgs as IntentUtteranceGetAllParams;
    return `intent-utterance-translations_${intentId}_${langId}_${JSON.stringify(rest)}`;
  },
  shouldUpdateCache: (queryArgs, initialArgs) => {
    return queryArgs.intentId === initialArgs.intentId && queryArgs.langId === initialArgs.langId;
  },
});

export const intentApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getIntentItems: builder.query<ApiResponse<PaginatedResponse<IntentItem>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/intent-items?${searchParams}`,
        };
      },
      ...intentItemMixins.paginated,
    }),

    getIntentItem: builder.query<ApiResponse<IntentItem>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-items/${id}` }),
      ...intentItemMixins.item,
    }),
    createIntentItem: builder.mutation<ApiResponse<IntentItem>, CreateIntentItemRequest>({
      query: body => ({
        url: '/intent-items',
        method: 'POST',
        body,
      }),
      ...intentItemMixins.create(),
    }),
    updateIntentItem: builder.mutation<
      ApiResponse<IntentItem>,
      UuidParams & UpdateIntentItemRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-items/${id}`,
        method: 'PUT',
        body,
      }),
      ...intentItemMixins.update(),
    }),
    deleteIntentItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-items/${id}`,
        method: 'DELETE',
      }),
      ...intentItemMixins.delete(),
    }),
    assignFlowToIntent: builder.mutation<ApiResponse<any>, AssignFlowToIntentRequest>({
      query: body => ({
        url: '/intent-items/assign-flow',
        method: 'POST',
        body,
      }),
      // ...intentItemMixins.update, //TODO: need to handle it for this
    }),

    // Intent Utterance Translations
    getIntentUtteranceTranslations: builder.query<
      ApiResponse<PaginatedResponse<IntentUtteranceTranslation>>,
      IntentUtteranceGetAllParams
    >({
      query: ({ intentId, langId, query = {} }) => {
        const { searchParams } = serializePaginationParams(query);
        return {
          url: `/intent/${intentId}/lang/${langId}/intent-utterance?${searchParams}`,
        };
      },
      ...utteranceMixins.paginated,
    }),

    getIntentUtteranceTranslation: builder.query<
      ApiResponse<IntentUtteranceTranslation>,
      UuidParams
    >({
      query: ({ id }) => ({
        url: `/intent-utterance/${id}`,
      }),
      ...utteranceMixins.item,
    }),
    createIntentUtteranceTranslation: builder.mutation<
      ApiResponse<IntentUtteranceTranslation>,
      CreateUtteranceTranslationRequest
    >({
      query: ({ intentId, langId, ...body }) => ({
        url: `/intent/${intentId}/lang/${langId}/intent-utterance`,
        method: 'POST',
        body,
      }),
      ...utteranceMixins.create(),
    }),
    updateIntentUtteranceTranslation: builder.mutation<
      ApiResponse<IntentUtteranceTranslation>,
      UuidParams & UpdateUtteranceTranslationRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-utterance/${id}`,
        method: 'PUT',
        body,
      }),
      ...utteranceMixins.update(),
    }),
    deleteIntentUtteranceTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-utterance/${id}`,
        method: 'DELETE',
      }),
      ...utteranceMixins.delete(),
    }),
    getTranslationByUtteranceIdAndLangId: builder.query<
      ApiResponse<IntentUtteranceTranslation>,
      UtteranceTranslationByLangParam
    >({
      query: ({ utteranceId, langId }) => ({
        url: `/utterance/${utteranceId}/lang/${langId}/translation`,
      }),
      providesTags: (result, error, { utteranceId, langId }) => [
        { type: 'IntentUtteranceTranslation', id: `${utteranceId}-${langId}` },
      ],
      // ...utteranceMixins.item, //TODO: need to handl this properly
    }),
  }),
});

export const {
  useGetIntentItemsQuery,
  useGetIntentItemQuery,
  useCreateIntentItemMutation,
  useUpdateIntentItemMutation,
  useDeleteIntentItemMutation,
  useAssignFlowToIntentMutation,

  useGetIntentUtteranceTranslationsQuery,
  useGetIntentUtteranceTranslationQuery,
  useCreateIntentUtteranceTranslationMutation,
  useUpdateIntentUtteranceTranslationMutation,
  useDeleteIntentUtteranceTranslationMutation,
  useGetTranslationByUtteranceIdAndLangIdQuery,
} = intentApi;
