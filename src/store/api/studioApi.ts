import type { Application, Template, PaginatedResponse } from '../../types';
import { apiSlice } from '../apiSlice';
import { ApiResponse } from '@/types/api.type';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Application', 'Template', 'AppDetails'],
});

export const studioApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getApplications: builder.query<
      PaginatedResponse<Application>,
      {
        page?: number;
        size?: number;
        token?: string;
        isAdmin?: boolean;
        isPlatform?: boolean;
      }
    >({
      query: ({ page = 1, size = 10, token, isAdmin = false, isPlatform = false }) => {
        const endpoint = isPlatform
          ? 'leap_gw/apps/getAppsForPlatform'
          : isAdmin
            ? 'leap_gw/apps/getAppsforAdmin'
            : 'leap_gw/apps/getApps';

        const params = new URLSearchParams({
          page: page.toString(),
          size: size.toString(),
          ...(token && { token }),
        });

        return { url: `${endpoint}?${params}` };
      },
      providesTags: ['Application'],
    }),

    updateApplicationDetails: builder.mutation<
      ApiResponse<Application>, // Response
      { appId: string; payload: any } // Request
    >({
      query: ({ appId, payload }) => {
        return {
          url: `/apps/${appId}`,
          method: 'PUT',
          body: payload,
        };
      },
      invalidatesTags: ['Application'],
    }),

    getPluginsEnabled: builder.query<any, void>({
      query: () => ({ url: `/apps/getPluginsEnabled` }),

      // providesTags: [""],
    }),

    getApplicationDetails: builder.query<
      ApiResponse<Application>, // Response
      { appId: string } // Request
    >({
      query: ({ appId }) => {
        return {
          url: `/apps/${appId}`,
          method: 'GET',
        };
      },
      providesTags: ['Application'],
    }),

    // Templates
    getTemplates: builder.query<{ data: Template[] }, void>({
      query: () => ({ url: 'leap_gw/apps/getTemplatesForStudio' }),
      providesTags: ['Template'],
    }),

    cloneTemplate: builder.mutation<
      { id: string },
      {
        name: string;
        appTemplateId: string;
        svg: string;
      }
    >({
      query: data => ({
        url: 'leap_gw/apps/cloneTemplate',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    // Platform Admin
    updateOTCAndMRC: builder.mutation<
      void,
      {
        id: string;
        nodeExecCharge: number;
        freeNodeExec: number;
        OTC: number;
        MRC: number;
      }
    >({
      query: data => ({
        url: 'leap_gw/apps/updateOTCAndMRC',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    // Logs
    getAppReports: builder.query<any, { appId: string }>({
      query: ({ appId }) => ({
        url: 'leap_gw/apps/getAppReports',
        method: 'POST',
        body: { appId },
      }),
    }),

    getDetailedAppReport: builder.query<
      any,
      {
        appId: string;
        page: number;
        size: number;
      }
    >({
      query: ({ appId, page, size }) => ({
        url: `leap_gw/apps/getDetailedAppReport?page=${page}&size=${size}`,
        method: 'POST',
        body: { appId, name: 'getAllData' },
      }),
    }),

    // Start URL
    getApplicationStartUrl: builder.query<
      {
        data: { url: { production: string } };
      },
      { payload: { engine: string; appId: string } }
    >({
      query: ({ payload }) => ({
        url: 'leap_gw/apps/getApplicationStartUrl',
        method: 'POST',
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetApplicationsQuery,
  useGetApplicationDetailsQuery,
  useGetTemplatesQuery,
  useCloneTemplateMutation,
  useUpdateOTCAndMRCMutation,
  useGetAppReportsQuery,
  useGetDetailedAppReportQuery,
  useGetApplicationStartUrlQuery,
  useGetPluginsEnabledQuery,
  useUpdateApplicationDetailsMutation,
} = studioApi;
