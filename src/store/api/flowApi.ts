import { FlowNode } from '@/types';
import { apiSlice } from '../apiSlice';
export interface UpdateBotRequest {
  name: string;
  domain: string;
  description: string;
}

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Flow'],
});

// --- Payload for creating a single flow ---
interface CreateSingleFlowPayload {
  name: string;
  description: string;
  botId: string;
  entryNodeId: string;
  nodes: Record<string, any>;
  metadata: Record<string, any>;
  type: 'Default' | 'Custom';
}

// --- API Slice ---
export const flowApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // GET all flows for a bot
    getFlows: builder.query<FlowNode[], { botId: string }>({
      query: ({ botId }) => ({ url: `/bots/${botId}/flows` }),
      transformResponse: (response: { data: { items: FlowNode[] } }) => response.data.items,
      providesTags: ['Flow'],
    }),

    // GET single flow by ID
    getFlowById: builder.query<FlowNode, { appId: string; flowId: string }>({
      query: ({ flowId }) => ({ url: `/flows/${flowId}` }),
      transformResponse: (response: { data: FlowNode }) => response.data,
      providesTags: ['Flow'],
    }),

    // POST - Create a new flow
    createFlow: builder.mutation<FlowNode, { botId: string }>({
      query: ({ botId }) => {
        return {
          url: `/flows`,
          method: 'POST',
          body: { botId },
        };
      },
      transformResponse: (response: { data: { appId: string; flow: FlowNode } }) => {
        return response.data.flow;
      },
      invalidatesTags: ['Flow'],
    }),

    // PUT - Update an existing flow
    updateFlow: builder.mutation<
      FlowNode,
      { appId: string; flowId: string; payload: Partial<CreateSingleFlowPayload> }
    >({
      query: ({ flowId, payload }) => {
        return {
          url: `/flows/${flowId}`,
          method: 'PUT',
          body: payload,
        };
      },
      transformResponse: (response: { data: FlowNode }) => response.data,
      invalidatesTags: ['Flow'],
    }),

    // DELETE - Delete a flow
    deleteFlow: builder.mutation<void, { appId: string; flowId: string }>({
      query: ({ appId, flowId }) => {
        return {
          url: `/flows/${flowId}/apps/${appId}`,
          method: 'DELETE',
        };
      },
      invalidatesTags: ['Flow'],
    }),

    // POST - Clone a flow
    cloneFlow: builder.mutation<FlowNode, { flowId: string }>({
      query: ({ flowId }) => ({
        url: `/flows/${flowId}/clone`,
        method: 'POST',
      }),
      transformResponse: (response: { data: FlowNode }) => response.data,
      invalidatesTags: ['Flow'],
    }),
  }),
});

// --- Export Hooks ---
export const {
  useGetFlowsQuery,
  useGetFlowByIdQuery,
  useCreateFlowMutation,
  useUpdateFlowMutation,
  useDeleteFlowMutation,
  useCloneFlowMutation,
} = flowApiSlice;
