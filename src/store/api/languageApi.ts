import {
  ApiResponse,
  BotLanguage,
  BulkCreateDeleteBotLanguagePayload,
  CreateBotLanguageRequest,
  CreateLanguageRequest,
  Language,
  PaginatedResponse,
  PaginationParams,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Language', 'BotLanguage'],
});

export const languageApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // Languages
    getLanguages: builder.query<ApiResponse<PaginatedResponse<Language>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/languages?${searchParams}`,
        };
      },
      providesTags: ['Language'],
    }),
    getLanguage: builder.query<ApiResponse<Language>, UuidParams>({
      query: ({ id }) => ({ url: `/languages/${id}` }),
      providesTags: ['Language'],
    }),

    // Bot Languages
    getBotLanguages: builder.query<ApiResponse<PaginatedResponse<BotLanguage>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/bot-languages?${searchParams}`,
        };
      },
      providesTags: ['BotLanguage'],
    }),
    getBotLanguage: builder.query<ApiResponse<BotLanguage>, UuidParams>({
      query: ({ id }) => ({ url: `/bot-languages/${id}` }),
      providesTags: ['BotLanguage'],
    }),
    createBotLanguage: builder.mutation<ApiResponse<BotLanguage>, CreateBotLanguageRequest>({
      query: ({ botId, langId }) => ({
        url: `/bot-languages/${botId}`,
        method: 'POST',
        body: { langId },
      }),
      invalidatesTags: ['BotLanguage'],
    }),
    deleteBotLanguage: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/bot-languages/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BotLanguage'],
    }),
    createBulkBotLanguages: builder.mutation<
      ApiResponse<BotLanguage[]>,
      BulkCreateDeleteBotLanguagePayload
    >({
      query: ({ botId, ids }) => ({
        url: `/bot-languages/${botId}/bulk`,
        method: 'POST',
        body: { ids },
      }),
      invalidatesTags: ['BotLanguage'],
    }),
    deleteBulkBotLanguages: builder.mutation<void, BulkCreateDeleteBotLanguagePayload>({
      query: ({ botId, ids }) => ({
        url: `/bot-languages/${botId}/bulk`,
        method: 'DELETE',
        body: { ids },
      }),
      invalidatesTags: ['BotLanguage'],
    }),
  }),
});

export const {
  // Languages
  useGetLanguagesQuery,
  useGetLanguageQuery,

  // Bot Languages
  useGetBotLanguagesQuery,
  useGetBotLanguageQuery,
  useCreateBotLanguageMutation,
  useDeleteBotLanguageMutation,
  useCreateBulkBotLanguagesMutation,
  useDeleteBulkBotLanguagesMutation,
} = languageApi;
