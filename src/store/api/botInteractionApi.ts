import { apiSlice } from '../apiSlice';
import { ApiSliceIdentifier } from '../helper';
import { SendMessageRequest, SendMessageResponse } from '@/types/botInteraction.type';

export const botInteractionApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    sendMessage: builder.mutation<any, { conversationId: string; payload: any }>({
      query: ({ conversationId, payload }) => ({
        url: `/conversations/${conversationId}/message`,
        method: 'POST',
        body: payload,
        apiSliceIdentifier: ApiSliceIdentifier.BOT_INTERACTION_SERVICE,
      }),
    }),
    resetConversation: builder.mutation<any, { conversationId: string }>({
      query: ({ conversationId }) => ({
        url: `/conversations/${conversationId}/cache`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.BOT_INTERACTION_SERVICE,
      }),
    }),
    sendPreviewMessage: builder.mutation<
      SendMessageResponse,
      { id: string; body: SendMessageRequest }
    >({
      query: ({ id, body }) => ({
        url: `preview/conversations/${id}/message`,
        method: 'POST',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.BOT_INTERACTION_SERVICE,
      }),
    }),
  }),
});

export const {
  useSendMessageMutation,
  useResetConversationMutation,
  useSendPreviewMessageMutation,
} = botInteractionApi;
