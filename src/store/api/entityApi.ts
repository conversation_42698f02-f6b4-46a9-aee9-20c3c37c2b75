import {
  ApiResponse,
  CreateEntityRequest,
  Entity,
  EntityPaginationParams,
  PaginatedResponse,
  UpdateEntityRequest,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';
import { createEntityMixins } from '../../lib/utils/optimizedTags';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Entity'],
});

const entityMixins = createEntityMixins<
  Entity,
  'Entity',
  typeof updatedApiSlice,
  EntityPaginationParams
>({
  entityType: 'Entity',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const { botId, page, ...rest } = queryArgs;
    return `entities_${botId}_${JSON.stringify(rest)}`;
  },
  options: {
    listEndpointName: 'getEntities',
  },
  shouldUpdateCache: (queryArgs, newEntity) => {
    return queryArgs.botId === newEntity.botId;
  },
});

export const entityApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getEntities: builder.query<ApiResponse<PaginatedResponse<Entity>>, EntityPaginationParams>({
      query: ({ botId, ...params }) => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/bots/${botId}/entities?${searchParams}`,
        };
      },
      ...entityMixins.paginated,
    }),
    getEntity: builder.query<ApiResponse<Entity>, UuidParams>({
      query: ({ id }) => ({ url: `/entities/${id}` }),
      ...entityMixins.item,
    }),
    createEntity: builder.mutation<ApiResponse<Entity>, CreateEntityRequest>({
      query: body => ({
        url: '/entities',
        method: 'POST',
        body,
      }),
      ...entityMixins.create(),
    }),
    updateEntity: builder.mutation<ApiResponse<Entity>, UuidParams & UpdateEntityRequest>({
      query: ({ id, ...body }) => ({
        url: `/entities/${id}`,
        method: 'PUT',
        body,
      }),
      ...entityMixins.update(),
    }),
    deleteEntity: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/entities/${id}`,
        method: 'DELETE',
      }),
      ...entityMixins.delete(),
    }),
  }),
});

export const {
  useGetEntitiesQuery,
  useGetEntityQuery,
  useCreateEntityMutation,
  useUpdateEntityMutation,
  useDeleteEntityMutation,
} = entityApi;
