import {
  createApi,
  fetchBaseQuery,
  type BaseQueryFn,
  type FetchBaseQueryError,
} from '@reduxjs/toolkit/query/react';
import type { RootState } from './store';
import { setCredentials, unsetCredentials, type AuthResData, rehydrateAuthFromSessionStorage } from './auth/authSlice';
import { ApiSliceIdentifier, getBaseUrl } from './helper';

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const baseQueryWithReauth: BaseQueryFn<
  { url: string; method?: string; body?: any; apiSliceIdentifier?: ApiSliceIdentifier },
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseUrl = getBaseUrl(args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders: (defaultHeaders, { getState }) => {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        defaultHeaders.set('Authorization', `Bearer ${token}`);
      }

      return defaultHeaders;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  if (result.error?.status === RESULT_ERROR_STATUS) {
    api.dispatch(rehydrateAuthFromSessionStorage());
    //TODO: we should rm the refresh api calls 
    // try to get a new token
    const refreshResult = await baseQuery(
      {
        url: baseUrl + '/auth/refresh',
        method: 'POST',
        body: { refreshToken: (api.getState() as RootState).auth.refreshToken },
      },
      api,
      extraOptions
    );

    if (refreshResult.data) {
      api.dispatch(setCredentials(refreshResult.data as AuthResData));

      result = await baseQuery(args, api, extraOptions);
    } else {
      await baseQuery(
        {
          url: baseUrl + '/auth/logout',
          method: 'POST',
          body: { refreshToken: (api.getState() as RootState).auth.refreshToken },
        },
        api,
        extraOptions
      );
      api.dispatch(unsetCredentials());
    }
  }
  return result;
};
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: _builder => ({}),
});
