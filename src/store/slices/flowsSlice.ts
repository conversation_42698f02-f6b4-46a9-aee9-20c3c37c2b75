import { FlowNode } from '@/types';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

interface FlowsState {
  activeFlowId: FlowNode | null;
}

const initialState: FlowsState = {
  activeFlowId: null,
};

const flowsSlice = createSlice({
  name: 'flows',
  initialState,
  reducers: {
    setActiveFlow: (state, action: PayloadAction<FlowNode | null>) => {
      state.activeFlowId = action.payload;
    },
  },
});

export const { setActiveFlow } = flowsSlice.actions;
export default flowsSlice.reducer;
