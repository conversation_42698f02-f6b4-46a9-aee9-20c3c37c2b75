import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  activeView: string;
  activeTab: string;
  showPreview: boolean;
  showMessage: boolean;
  showForm: boolean;
  isEditingTitle: boolean;
  selectedFlowDropdown: string | null;
}

const initialState: UIState = {
  activeView: 'neuratalk',
  activeTab: 'Design',
  showPreview: false,
  showMessage: false,
  showForm: false,
  isEditingTitle: false,
  selectedFlowDropdown: null,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setActiveView: (state, action: PayloadAction<string>) => {
      state.activeView = action.payload;
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },
    togglePreview: state => {
      state.showPreview = !state.showPreview;
    },
    showMessage: (state, action: PayloadAction<boolean>) => {
      state.showMessage = action.payload;
    },
    toggleFormNode: state => {
      state.showForm = !state.showForm;
    },
    setEditingTitle: (state, action: PayloadAction<boolean>) => {
      state.isEditingTitle = action.payload;
    },
    setSelectedFlowDropdown: (state, action: PayloadAction<string | null>) => {
      state.selectedFlowDropdown = action.payload;
    },
  },
});

export const {
  setActiveView,
  setActiveTab,
  togglePreview,
  showMessage,
  toggleFormNode,
  setEditingTitle,
  setSelectedFlowDropdown,
} = uiSlice.actions;
export default uiSlice.reducer;
