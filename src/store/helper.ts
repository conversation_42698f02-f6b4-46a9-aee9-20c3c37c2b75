import { RootState } from './store';

export enum ApiSliceIdentifier {
  BOT_BUILDER_SERVICE,
  BOT_INTERACTION_SERVICE,
}

export function getBaseUrl(apiSliceIdentifier?: ApiSliceIdentifier) {
  //TODO: need to handle it through env
  const baseUrlMap: Partial<Record<ApiSliceIdentifier, string | undefined>> = {
    [ApiSliceIdentifier.BOT_BUILDER_SERVICE]: 'http://localhost:3000/api/v1',
    [ApiSliceIdentifier.BOT_INTERACTION_SERVICE]: 'http://localhost:3001/api/v1',
  };
  if (!Number.isInteger(apiSliceIdentifier))
    return baseUrlMap[ApiSliceIdentifier.BOT_BUILDER_SERVICE];

  return baseUrlMap[apiSliceIdentifier!];
}
