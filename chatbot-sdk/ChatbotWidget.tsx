import React, { useCallback, useRef, useState } from 'react';
import { ChatbotConfig } from './types/chatbot-sdk.type';
import { ChatMessages, ConfirmDialog } from '@/modules/Preview';
import ChatInput from './components/ChatbotInput';
import ChatbotHeader from './components/ChatbotHeader';
import { cn } from '@/lib/utils';
import { useChatbot } from './hooks/useChatbot';

interface ChatbotWidgetProps {
  config: ChatbotConfig;
  onClose: () => void;
  onMinimize: () => void;
}

const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({ config, onClose, onMinimize }) => {
  // Validate botId presence
  if (!config.botId) {
    return (
      <div className="flex items-center justify-center h-full w-full text-red-500 text-center p-4">
        Error: botId is missing in the chatbot configuration. Please provide a valid botId.
      </div>
    );
  }

  const {
    messages,
    isLoading,
    error,
    lastFormPrompt,
    lastFormFieldValues,
    formLocked,
    isFormActive,
    chatForm,
    isFormValid,
    handleChatInputSubmit,
    handleFormPromptSubmit,
    resetConversation,
    isSingleTextField,
    setIsFormActive,
  } = useChatbot({ config });

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const portalContainerRef = useRef<HTMLDivElement>(null);

  const handleClose = useCallback(() => {
    if (messages.length > 0) setShowConfirmDialog(true);
    else {
      resetConversation();
      onClose();
    }
  }, [messages, resetConversation, onClose]);

  const confirmClose = useCallback(
    async (confirm: boolean) => {
      setShowConfirmDialog(false);
      if (confirm) {
        await resetConversation();
      }
      onClose();
    },
    [resetConversation, onClose]
  );

  // Apply theming from config
  const chatWidgetStyle: React.CSSProperties = {
    '--primary-color': config.primaryColor || '#007bff',
    '--secondary-color': config.secondaryColor || '#6c757d',
    '--bot-bubble-color': config.botBubbleColor || '#e9ecef',
    '--user-bubble-color': config.userBubbleColor || '#007bff',
    '--font-family': config.fontFamily || 'sans-serif',
    '--font-size': config.fontSize || '1rem',
  } as React.CSSProperties;

  const themeClass = config.theme === 'dark' ? 'theme-dark' : 'theme-light';

  return (
    <div
      className={cn(
        'w-96 h-[500px] flex flex-col border bg-background rounded-lg shadow-lg overflow-hidden scale-100',
        themeClass
      )}
      style={chatWidgetStyle}
      ref={portalContainerRef}
    >
      <ChatbotHeader config={config} onClose={handleClose} onMinimize={onMinimize} />

      <div className="flex-1 p-4 overflow-auto">
        <div className="bg-background rounded-lg h-full flex flex-col">
          <ChatMessages
            messages={messages}
            lastFormPrompt={lastFormPrompt}
            formLocked={formLocked}
            onFormSubmit={handleFormPromptSubmit}
            lastFormFieldValues={lastFormFieldValues}
            loading={isLoading}
            onFormActiveChange={setIsFormActive}
          />
          {error && <div className="text-error-500 text-xs mt-2">{error}</div>}
          <ChatInput
            activeForm={lastFormPrompt}
            loading={isLoading}
            isFormValid={isFormValid}
            disabled={isFormActive}
            isSingleTextField={!!isSingleTextField}
            onSubmit={handleChatInputSubmit}
            form={chatForm}
            config={config}
          />
        </div>
      </div>

      {showConfirmDialog && (
        <ConfirmDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
          onConfirm={confirmClose}
          container={portalContainerRef.current}
        />
      )}
    </div>
  );
};

export default ChatbotWidget;
