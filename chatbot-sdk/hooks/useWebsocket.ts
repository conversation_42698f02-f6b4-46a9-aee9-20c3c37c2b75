import { useState, useEffect, useRef, useCallback } from 'react';
import { SendMessageRequest, SendMessageResponse } from '../../src/types/botInteraction.type';

interface WebSocketChatState {
  isConnected: boolean;
  error: string | null;
  lastMessage: SendMessageResponse | null;
  send: ((message: SendMessageRequest) => void) | null;
}

//TODO: need to get websocket url from env
export const useWebSocketChat = (
  webSocketUrl: string,
  conversationId: string,
  token?: string
): WebSocketChatState => {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastMessage, setLastMessage] = useState<SendMessageResponse | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const connect = useCallback(() => {
    if (!webSocketUrl) {
      setError('WebSocket URL is not provided.');
      return;
    }
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      return;
    }

    const ws = new WebSocket(webSocketUrl);

    ws.onopen = () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setError(null);
      ws.send(JSON.stringify({ type: 'connect', conversationId, token }));
    };

    ws.onmessage = event => {
      try {
        const message: SendMessageResponse = JSON.parse(event.data);
        setLastMessage(message);
      } catch (e) {
        console.error('Failed to parse WebSocket message:', e);
        setError('Failed to parse WebSocket message.');
      }
    };

    ws.onerror = event => {
      console.error('WebSocket error:', event);
      setError('WebSocket connection error.');
      setIsConnected(false);
    };

    ws.onclose = event => {
      console.log('WebSocket disconnected:', event);
      setIsConnected(false);
      if (!event.wasClean) {
        setError('WebSocket connection unexpectedly closed.');
      }
    };

    wsRef.current = ws;
  }, [webSocketUrl, conversationId, token]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  }, []);

  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  const send = useCallback((message: SendMessageRequest) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      setError('WebSocket is not connected.');
    }
  }, []);

  return { isConnected, error, lastMessage, send };
};
