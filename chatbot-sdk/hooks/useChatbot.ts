import { useState, useCallback, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { ChatbotConfig } from '../types/chatbot-sdk.type';
import { useSendPreviewMessage, useResetConversation } from '../api';
import { useWebSocketChat } from './useWebsocket';
import {
  createUserMessage,
  createFormSubmissionPayload,
  createTextMessagePayload,
  createSingleFieldFormPayload,
  processServerResponse,
  isSingleTextFieldForm,
  hasBlockingForm,
  RendererType,
  BotFormFields,
  ChatMessage,
} from '@/modules/Preview';

interface UseChatbotProps {
  config: ChatbotConfig;
}

const SESSION_STORAGE_KEY = 'chatbot_conversation_id';

export const useChatbot = ({ config }: UseChatbotProps) => {
  const {
    token,
    botId,
    onSessionStart,
    initialMessage,
    placeholderText,
    onMessageReceived,
    onMessageSent,
    onSessionEnd,
    sessionId,
    useWebSocket,
    webSocketUrl,
  } = config;

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sendPreviewMessageApi, { isLoading: isSendingMessage }] = useSendPreviewMessage(token);
  const [resetConversationApi, { isLoading: isResettingConversation }] =
    useResetConversation(token);
  const [error, setError] = useState<string | null>(null);
  const [lastFormPrompt, setLastFormPrompt] = useState<ChatMessage | null>(null);
  const [lastFormFieldValues, setLastFormFieldValues] = useState<BotFormFields | undefined>(
    undefined
  );
  const [formLocked, setFormLocked] = useState(false);
  const [isFormActive, setIsFormActive] = useState(false);
  const [isFeedbackForm, setIsFeedbackForm] = useState(false);
  const [wsLoading, setWsLoading] = useState(false);

  const [conversationId, setConversationId] = useState<string>(() => {
    if (sessionId) {
      return sessionId;
    }
    const storedId = localStorage.getItem(SESSION_STORAGE_KEY);
    if (storedId) {
      return storedId;
    }
    const newId = crypto.randomUUID();
    localStorage.setItem(SESSION_STORAGE_KEY, newId);
    return newId;
  });
  const conversationIdRef = useRef(conversationId);

  // Initialize WebSocket hook conditionally
  const {
    isConnected: wsIsConnected,
    error: wsError,
    lastMessage: wsLastMessage,
    send: wsSend,
  } = useWebSocket
    ? useWebSocketChat(webSocketUrl!, conversationId, token)
    : { isConnected: false, error: null, lastMessage: null, send: null };

  useEffect(() => {
    conversationIdRef.current = conversationId;
  }, [conversationId]);

  const isLoading = isSendingMessage || isResettingConversation || wsLoading; // Adjust isLoading

  const chatForm = useForm<{ message: string }>({
    defaultValues: { message: placeholderText || '' },
  });
  const { setValue } = chatForm;

  // Trigger onSessionStart when conversationId is first established
  useEffect(() => {
    if (onSessionStart && conversationId) {
      onSessionStart(conversationId);
    }
  }, [conversationId, onSessionStart]);

  // Initialize with initial message if provided
  useEffect(() => {
    if (initialMessage && messages.length === 0) {
      setMessages([
        {
          sender: 'bot', // Assuming initial message is from bot
          nodeType: RendererType.MESSAGE,
          data: { text: initialMessage },
        } as ChatMessage,
      ]);
    }
  }, [initialMessage, messages.length]);

  // Process WebSocket messages
  useEffect(() => {
    if (useWebSocket && wsLastMessage) {
      setWsLoading(false); // Set WebSocket loading to false on message received
      const botMessages = processServerResponse(
        (wsLastMessage as any)?.data?.response || (wsLastMessage as any)?.response || []
      );
      setMessages(prev => {
        const newMessages = [...prev, ...botMessages];
        botMessages.forEach(msg => {
          if (onMessageReceived) {
            onMessageReceived(msg);
          }
        });
        return newMessages;
      });

      const formMessage = botMessages.find(m => m.nodeType === RendererType.FORM);
      const feedbackMessage = botMessages.find(m => m.nodeType === RendererType.FEEDBACK);

      if (formMessage) {
        setLastFormPrompt(formMessage);
        const initial: BotFormFields = {};
        formMessage.data.prompt.forEach((field: any) => (initial[field.fieldName] = ''));
        setLastFormFieldValues(initial);
      } else if (feedbackMessage) {
        setLastFormPrompt(feedbackMessage);
        setLastFormFieldValues({ feedback: '' });
        setIsFeedbackForm(true);
      } else {
        setLastFormPrompt(null);
        setLastFormFieldValues(undefined);
        setIsFeedbackForm(false);
      }
      setFormLocked(false); // Unlock form after receiving WebSocket response
    }
  }, [useWebSocket, wsLastMessage, onMessageReceived]);

  const isFormValid = useCallback((): boolean => {
    if (!lastFormPrompt) return false;
    if (isFeedbackForm) {
      return Boolean(lastFormFieldValues?.feedback);
    }
    // Type guard to ensure we have FormData
    if (lastFormPrompt.nodeType === RendererType.FORM && 'prompt' in lastFormPrompt.data) {
      return lastFormPrompt.data.prompt.every(
        (field: any) => !field.required || lastFormFieldValues?.[field.fieldName]
      );
    }
    return false;
  }, [lastFormPrompt, lastFormFieldValues, isFeedbackForm]);

  const sendMessageToServer = useCallback(
    async (body: any) => {
      setError(null);
      if (useWebSocket && wsSend) {
        setWsLoading(true); // Set WebSocket loading to true
        wsSend(body);
      } else {
        try {
          const data = await sendPreviewMessageApi(conversationIdRef.current, body);

          const botMessages = processServerResponse(
            (data as any)?.data?.response || (data as any)?.response || []
          );
          setMessages(prev => {
            const newMessages = [...prev, ...botMessages];
            // Trigger onMessageReceived for each bot message
            botMessages.forEach(msg => {
              if (onMessageReceived) {
                onMessageReceived(msg);
              }
            });
            return newMessages;
          });

          const formMessage = botMessages.find(m => m.nodeType === RendererType.FORM);
          const feedbackMessage = botMessages.find(m => m.nodeType === RendererType.FEEDBACK);

          if (formMessage) {
            setLastFormPrompt(formMessage);
            const initial: BotFormFields = {};
            formMessage.data.prompt.forEach((field: any) => (initial[field.fieldName] = ''));
            setLastFormFieldValues(initial);
          } else if (feedbackMessage) {
            setLastFormPrompt(feedbackMessage);
            setLastFormFieldValues({ feedback: '' });
            setIsFeedbackForm(true);
          } else {
            setLastFormPrompt(null);
            setLastFormFieldValues(undefined);
            setIsFeedbackForm(false);
          }
        } catch (err: any) {
          setError(err.message || 'An error occurred.');
        } finally {
          setFormLocked(false);
        }
      }
    },
    [useWebSocket, wsSend, sendPreviewMessageApi, onMessageReceived]
  );

  const handleChatInputSubmit = useCallback(
    async (fields: { message: string }) => {
      if (isLoading) return; // Use combined isLoading

      const userInput = fields.message.trim();
      if (!userInput) return;

      const isSingleTextField = isSingleTextFieldForm(lastFormPrompt);

      if (lastFormPrompt && isSingleTextField) {
        const fieldName = (lastFormPrompt.data as any).prompt[0].fieldName; // Cast to any for now

        const userMsg = createUserMessage(userInput);
        setMessages(prev => [...prev, userMsg]);
        // Trigger onMessageSent
        if (onMessageSent) {
          onMessageSent(userMsg);
        }

        const formPayload = createSingleFieldFormPayload(
          fieldName,
          userInput,
          botId,
          conversationIdRef.current,
          (lastFormPrompt.data as any).formId
        );

        await sendMessageToServer(formPayload);
        chatForm.reset({ message: '' });
        setValue('message', '');
        return;
      }

      if (hasBlockingForm(lastFormPrompt)) {
        return; // multi-field form → block sending
      }

      const userMsg = createUserMessage(userInput);
      setMessages(prev => [...prev, userMsg]);
      // Trigger onMessageSent
      if (onMessageSent) {
        onMessageSent(userMsg);
      }

      const textPayload = createTextMessagePayload(userInput, botId, conversationIdRef.current);
      await sendMessageToServer(textPayload);

      chatForm.reset({ message: '' });
      setValue('message', '');
    },
    [isLoading, sendMessageToServer, chatForm, lastFormPrompt, setValue, onMessageSent, botId] // Use combined isLoading
  );

  const handleFormPromptSubmit = useCallback(
    async (fields: BotFormFields) => {
      if (!lastFormPrompt || isLoading) return; // Add null check for lastFormPrompt

      const formPayload = createFormSubmissionPayload(
        fields,
        botId,
        conversationIdRef.current,
        lastFormPrompt.nodeType === RendererType.FORM
          ? (lastFormPrompt.data as any)?.formId
          : undefined
      );

      setMessages(prev =>
        prev.map(msg => {
          if (msg === lastFormPrompt && msg.nodeType === RendererType.FORM) {
            return {
              ...msg,
              data: {
                ...(msg.data as any), // Cast to any for now
                submittedValues: formPayload.formData,
              },
            };
          }
          return msg;
        })
      );

      setLastFormFieldValues(fields);
      setFormLocked(true);

      await sendMessageToServer(formPayload);
    },
    [isLoading, lastFormPrompt, sendMessageToServer, botId] // Use combined isLoading
  );

  const resetConversation = useCallback(async () => {
    try {
      if (useWebSocket && wsSend) {
        wsSend({
          botId: botId, // Assuming botId is available in scope
          content: '',
          metadata: { action: 'reset', conversationId: conversationIdRef.current },
        });
      } else {
        await resetConversationApi(conversationIdRef.current);
      }
      setMessages([]);
      setValue('message', '');
      setLastFormPrompt(null);
      setLastFormFieldValues(undefined);
      setFormLocked(false);
      setError(null);
      // Trigger onSessionEnd before resetting
      if (onSessionEnd) {
        onSessionEnd(conversationIdRef.current);
      }
      const newId = crypto.randomUUID();
      localStorage.setItem(SESSION_STORAGE_KEY, newId);
      setConversationId(newId);
      setIsFeedbackForm(false);
    } catch (err) {
      console.error('Reset failed:', err);
      if (!useWebSocket) {
        // Only set error for REST API calls
        setError((err as any).message || 'Reset failed.');
      }
    }
  }, [useWebSocket, wsSend, resetConversationApi, setValue, onSessionEnd, setConversationId]);

  const isSingleTextField = isSingleTextFieldForm(lastFormPrompt);

  return {
    messages,
    isLoading,
    error: error || wsError,
    lastFormPrompt,
    lastFormFieldValues,
    formLocked,
    isFormActive,
    isFeedbackForm,
    conversationId,
    chatForm,
    isFormValid,
    sendMessageToServer,
    handleChatInputSubmit,
    handleFormPromptSubmit,
    resetConversation,
    isSingleTextField,
    setIsFormActive,
  };
};
