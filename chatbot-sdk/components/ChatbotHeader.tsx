import React, { memo } from 'react';
import { X, Minus } from 'lucide-react';
import { ChatbotConfig } from '../types/chatbot-sdk.type';
import DefaultBotImage from '@/assets/icons/bot.svg';

interface ChatbotHeaderProps {
  config: ChatbotConfig;
  onClose: () => void;
  onMinimize: () => void;
}

const ChatbotHeader: React.FC<ChatbotHeaderProps> = memo(({ config, onClose, onMinimize }) => {
  const { botName, botDescription, botAvatarUrl } = config;

  return (
    <div
      className="px-4 py-2 border-b border-secondary-200 flex items-center justify-between"
      style={{ backgroundColor: config.primaryColor, color: config.tertiaryColor }}
    >
      <div className="flex items-center gap-2">
        <img
          src={botAvatarUrl || DefaultBotImage}
          alt={botName || 'Bot Avatar'}
          className="w-12 h-1w-12 rounded-full"
        />

        <div>
          <h3>{botName}</h3>
          {botDescription && <p className="text-xs">{botDescription}</p>}
        </div>
      </div>
      <div className="flex items-center space-x-2">
        {/* Minimize/Maximize Button */}
        <button onClick={onMinimize}>
          <Minus />
        </button>
        <button onClick={onClose}>
          <X className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
});

export default ChatbotHeader;
