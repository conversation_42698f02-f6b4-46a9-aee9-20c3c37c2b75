import React, { memo } from 'react';
import { Smile, Send } from 'lucide-react';
// Removed: import { useTranslation } from 'react-i18next';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { UseFormReturn } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { ChatMessage } from '@/modules/Preview/types';
import { ChatbotConfig } from '../types/chatbot-sdk.type';

interface ChatInputProps {
  activeForm: ChatMessage | null;
  loading: boolean;
  isFormValid: () => boolean;
  isSingleTextField: boolean;
  onSubmit: (fields: { message: string }) => void;
  form: UseFormReturn<any>;
  disabled?: boolean;
  config: ChatbotConfig;
}

const ChatInput: React.FC<ChatInputProps> = memo(
  ({ activeForm, loading, isSingleTextField, isFormValid, disabled, onSubmit, form, config }) => {
    const isButtonDisabled = loading || !!(activeForm && !isFormValid());
    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="pt-1 border-t" autoComplete="off">
          <div className="flex flex-row gap-1 items-center rounded-lg px-2 py-1">
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input
                      type="text"
                      placeholder={
                        activeForm ? 'Fill the form above to continue' : 'Enter your message...'
                      }
                      className="border-none px-0 w-60 text-sm bg-transparent focus:outline-none"
                      {...field}
                      disabled={disabled || loading || (!isSingleTextField && disabled)}
                      aria-label="Type your message"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button
              type="button"
              variant="ghost"
              className="w-9 h-9 p-0"
              tabIndex={-1}
              disabled={disabled || isButtonDisabled}
            >
              <Smile className="!w-6 !h-6" />
            </Button>

            <Button
              type="submit"
              className="px-2 py-2 flex justify-center items-center rounded-full"
              disabled={isButtonDisabled}
              aria-label="Send"
            >
              <Send className="!w-5 !h-5" style={{ fill: config.tertiaryColor }} />
            </Button>
          </div>
        </form>
      </Form>
    );
  }
);

export default ChatInput;
