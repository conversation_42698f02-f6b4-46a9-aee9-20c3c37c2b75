import { useState, useCallback, useEffect } from 'react';
import { SendMessageRequest, SendMessageResponse } from '../src/types/botInteraction.type';

const BASE_URL = 'http://localhost:3001/api/v1'; //TODO: get this from env

interface ApiState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
}

interface UseApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: HeadersInit;
  body?: any;
  skip?: boolean;
}

interface UseApiResult<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  fetchData: (dynamicId?: string, newBody?: any) => Promise<T | null>;
}

// Helper to build the final request options
const buildRequest = (method: string, headers?: HeadersInit, body?: any): RequestInit => {
  const init: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...(headers || {}),
    },
  };
  if (body) {
    init.body = JSON.stringify(body);
  }
  return init;
};

export const useApi = <T>(
  path: string | ((dynamicId: string) => string),
  options?: UseApiOptions
): UseApiResult<T> => {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const fetchData = useCallback(
    async (dynamicId?: string, newBody?: any): Promise<T | null> => {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      try {
        const finalPath = typeof path === 'function' ? path(dynamicId!) : path;
        const response = await fetch(
          `${BASE_URL}${finalPath}`,
          buildRequest(options?.method || 'GET', options?.headers, newBody ?? options?.body)
        );

        const responseData = await response.json();

        if (!response.ok) {
          throw new Error(responseData.message || 'API request failed');
        }

        setState({ data: responseData, isLoading: false, error: null });
        return responseData;
      } catch (err: any) {
        setState({ data: null, isLoading: false, error: err.message || 'Unknown error' });
        return null;
      }
    },
    [path, options?.method, options?.headers, options?.body]
  );

  useEffect(() => {
    if (!options?.skip && options?.method === 'GET') {
      fetchData();
    }
  }, [fetchData, options?.skip, options?.method]);

  return {
    data: state.data,
    isLoading: state.isLoading,
    error: state.error,
    fetchData,
  };
};

// Reusable helper for authenticated requests
const authHeaders = (token?: string): HeadersInit | undefined =>
  token ? { Authorization: `Bearer ${token}` } : undefined;

// ---- Hook: Send Preview Message ----
export const useSendPreviewMessage = (
  token?: string
): [
  (id: string, body: SendMessageRequest) => Promise<SendMessageResponse | null>,
  ApiState<SendMessageResponse>,
] => {
  const { fetchData, ...state } = useApi<SendMessageResponse>(
    id => `/conversations/${id}/message`,
    {
      method: 'POST',
      headers: authHeaders(token),
      skip: true,
    }
  );

  const sendRequest = useCallback(
    (id: string, body: SendMessageRequest) => fetchData(id, body),
    [fetchData]
  );

  return [sendRequest, state];
};

export const useResetConversation = (
  token?: string
): [(conversationId: string) => Promise<any | null>, ApiState<any>] => {
  const { fetchData, ...state } = useApi<any>(id => `/conversations/${id}/cache`, {
    method: 'DELETE',
    headers: authHeaders(token),
    skip: true,
  });

  const reset = useCallback((conversationId: string) => fetchData(conversationId), [fetchData]);

  return [reset, state];
};
