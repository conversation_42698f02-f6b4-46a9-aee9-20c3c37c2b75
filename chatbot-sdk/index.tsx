import React from 'react';
import { createRoot } from 'react-dom/client';
import { ChatbotConfig } from './types/chatbot-sdk.type';
import ChatbotContainer from './ChatbotContainer';

interface ChatbotSDKInterface {
  init: (containerId: string, config: ChatbotConfig) => void;
}

declare global {
  interface Window {
    ChatbotSDK: ChatbotSDKInterface;
  }
}

const ChatbotSDK: ChatbotSDKInterface = {
  init: (containerId: string, config: ChatbotConfig) => {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`ChatbotSDK: Container element with ID "${containerId}" not found.`);
      return;
    }

    const root = createRoot(container);
    root.render(
      <React.StrictMode>
        <div className="h-full w-full mfe-app">
          <ChatbotContainer config={config} />
        </div>
      </React.StrictMode>
    );
  },
};

export default ChatbotSDK;
