import React, { useState, useCallback } from 'react';
import ChatbotWidget from './ChatbotWidget';
import { ChatbotConfig } from './types/chatbot-sdk.type';
import { cn } from '@/lib/utils';
import { MessageSquare } from 'lucide-react';

interface ChatbotContainerProps {
  config: ChatbotConfig;
}

const ChatbotContainer: React.FC<ChatbotContainerProps> = ({ config }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const toggleChat = useCallback(() => {
    setIsOpen(prev => !prev);
    if (isOpen) {
      // If closing, ensure it's not minimized for next open
      setIsMinimized(false);
    }
  }, [isOpen]);

  const handleWidgetAppearance = useCallback(() => {
    if (!isOpen) {
      setIsOpen(true);
    } else {
      setIsMinimized(prev => !prev);
    }
  }, [isOpen]);

  const toggleMinimize = useCallback(() => {
    setIsMinimized(prev => !prev);
  }, []);

  return (
    <div className="fixed bottom-5 right-5 z-[1000]">
      {/* Main container for launcher and chat window */}
      {isOpen && (
        <div
          className={cn(
            'fixed  shadow-lg rounded-lg overflow-hidden flex flex-col bg-white border border-tertiary-200 transition-all duration-300 ease-in-out',
            'w-96 h-[500px]',
            'md:w-96 md:h-[500px]',
            'sm:w-full sm:h-full bottom-0 right-0 sm:bottom-24 sm:right-8 sm:rounded-none',
            isMinimized && 'hidden'
          )}
        >
          <ChatbotWidget config={config} onClose={toggleChat} onMinimize={toggleMinimize} />
        </div>
      )}
      <button
        onClick={handleWidgetAppearance}
        className="w-16 h-16 rounded-full text-white border-none cursor-pointer text-2xl flex items-center justify-center shadow-md"
        style={{
          backgroundColor: config.primaryColor || '#496FDB',
        }}
      >
        {isOpen && !isMinimized ? '✕' : <MessageSquare className="w-7 h-7" />}
      </button>
    </div>
  );
};

export default ChatbotContainer;
